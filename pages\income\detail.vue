<template>
	<view class="detail-container">
		<!-- 顶部导航 -->
		<view class="header">
			<view class="nav-left" @click="goBack">
				<text class="nav-icon">←</text>
				<text class="nav-text">返回</text>
			</view>
			<view class="nav-title">收入详情</view>
			<view class="nav-right">
				<view class="action-btn save-btn" @click="saveRecord">
					<text class="btn-text">保存</text>
				</view>
			</view>
		</view>

		<!-- 详情表单 -->
		<view class="form-container">
			<!-- 日期工作汇总 -->
			<view class="section daily-summary">
				<view class="section-title">
					<view class="title-content">
						<text class="section-icon">📊</text>
						<text class="title-text">工作概况</text>
					</view>
					<view class="title-line"></view>
				</view>

				<!-- 日期汇总信息卡片 -->
				<view class="daily-summary-card">
					<!-- 日期和基本信息 -->
					<view class="summary-header">
						<view class="date-display">
							<text class="date-icon">📅</text>
							<text class="date-text">{{ selectedDate || '未设置日期' }}</text>
						</view>
						<view class="worker-count">
							<text class="count-icon">👥</text>
							<text class="count-text">{{ workRecords.length }}名工人</text>
						</view>
					</view>

					<!-- 工人列表 -->
					<view class="workers-list" v-if="workRecords.length > 0">
						<view class="workers-header">
							<text class="workers-title">参与工人</text>
							<view class="expand-btn" v-if="showExpandButton" @tap="toggleWorkersExpanded">
								<text class="expand-text">{{ expandButtonText }}</text>
							</view>
						</view>
						<view class="workers-tags">
							<view class="worker-tag" v-for="(workRecord, index) in displayedWorkers"
								:key="`worker-${index}-${workRecord.id || workRecord.worker_name}`">
								<text class="worker-name">{{ workRecord.worker_name }}</text>
								<text class="worker-production">{{ calculateWorkRecordProduction(workRecord).toFixed(1)
								}}斤</text>
							</view>
						</view>
					</view>

					<!-- 汇总数据 -->
					<view class="summary-stats">
						<!-- 第一行：上午产量、下午产量 -->
						<view class="stats-row">
							<view class="stat-item production-stat">
								<view class="stat-icon">🌅</view>
								<view class="stat-content">
									<text class="stat-label">上午产量</text>
									<text class="stat-value">{{ morningProduction.toFixed(2) }} 斤</text>
								</view>
							</view>

							<view class="stat-item production-stat">
								<view class="stat-icon">🌇</view>
								<view class="stat-content">
									<text class="stat-label">下午产量</text>
									<text class="stat-value">{{ afternoonProduction.toFixed(2) }} 斤</text>
								</view>
							</view>
						</view>
						<!-- 第二行：日总产量、日总工钱 -->
						<view class="stats-row">
							<view class="stat-item production-stat">
								<view class="stat-icon">⚖️</view>
								<view class="stat-content">
									<text class="stat-label">日总产量</text>
									<text class="stat-value primary">{{ totalProduction.toFixed(2) }} 斤</text>
								</view>
							</view>
							<view class="stat-item cost-stat">
								<view class="stat-icon">💰</view>
								<view class="stat-content">
									<text class="stat-label">日总工钱</text>
									<text class="stat-value secondary">¥{{ dailyLaborCost.toFixed(2) }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 销售模式选择 -->
			<view class="section sales-mode">
				<view class="section-title">
					<view class="title-content">
						<text class="section-icon">🔄</text>
						<text class="title-text">销售模式</text>
					</view>
					<view class="title-line"></view>
				</view>

				<view class="mode-selector">
					<view class="mode-options">
						<view v-for="option in modeOptions" :key="option.value" class="mode-option"
							:class="{ 'active': salesMode === option.value }" @tap="switchSalesMode(option.value)">
							<view class="mode-header">
								<text class="mode-label">{{ option.label }}</text>
								<view class="mode-indicator" v-if="salesMode === option.value">
									<text class="indicator-icon">✓</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 销售记录管理 -->
			<view class="section sales-records">
				<view class="section-title">
					<view class="title-content">
						<text class="section-icon">🛒</text>
						<text class="title-text">销售管理</text>
						<view class="sales-summary">
							<text class="sales-count">{{ salesRecords.length }}客户</text>
							<text class="sales-allocation">{{ allocatedProduction.toFixed(1) }}/{{
								totalProduction.toFixed(1) }}斤</text>
						</view>
					</view>
					<view class="title-line"></view>
				</view>

				<!-- 紧凑型销售记录列表 -->
				<view class="compact-sales-list">
					<view v-for="(record, index) in salesRecords" :key="`sales-${index}-${record.id || 'new'}`"
						class="compact-sales-item"
						:class="{ 'error': isProductionOverLimit(record.production, index) }">

						<!-- 客户标识和删除按钮 -->
						<view class="item-header">
							<view class="customer-badge">
								<text class="customer-number">{{ index + 1 }}</text>
							</view>
							<view class="item-actions">
								<view v-if="salesRecords.length > 1" class="delete-btn" @tap="removeSalesRecord(index)">
									<text class="delete-icon">×</text>
								</view>
							</view>
						</view>

						<!-- 紧凑型输入区域 -->
						<view class="compact-inputs">
							<!-- 产量输入 -->
							<view class="input-group production-group">
								<text class="input-label">产量</text>
								<view class="input-container">
									<input class="compact-input"
										:class="{ 'error': isProductionOverLimit(record.production, index) }"
										type="number" v-model.number="record.production" placeholder="0.0" :min="0"
										:max="totalProduction" :step="0.1" @input="validateProductionInput(index)"
										@blur="validateProductionBlur(index)" />
									<text class="input-unit">斤</text>
								</view>
							</view>

							<!-- 单价输入 -->
							<view class="input-group price-group">
								<text class="input-label">单价</text>
								<view class="input-container">
									<input class="compact-input" type="number" v-model.number="record.selling_price"
										placeholder="0.0" :min="0" :step="0.1" />
									<text class="input-unit">¥/斤</text>
								</view>
							</view>

							<!-- 客户名称输入 -->
							<view class="input-group name-group">
								<text class="input-label">客户</text>
								<view class="input-container">
									<input class="compact-input name-input" type="text" v-model="record.customer_name"
										:placeholder="`客户${index + 1}`" />
								</view>
							</view>
						</view>

						<!-- 小计和状态 -->
						<view class="item-footer">
							<view class="subtotal-info">
								<text class="subtotal-amount">¥{{ ((record.selling_price || 0) * (record.production ||
									0)).toFixed(2) }}</text>
								<text v-if="isProductionOverLimit(record.production, index)"
									class="error-text">超限</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 添加按钮和状态 -->
				<view class="add-section">
					<view class="add-btn" @tap="addSalesRecord">
						<text class="add-icon">+</text>
						<text class="add-text">添加客户</text>
					</view>
					<view class="allocation-status">
						<text class="status-text" :class="{ 'warning': isOverAllocated }">
							{{ allocationStatusText }}
						</text>
					</view>
				</view>
			</view>

			<!-- 收益汇总 -->
			<view class="section profit-summary">
				<view class="section-title">
					<view class="title-content">
						<text class="section-icon">📈</text>
						<text class="title-text">收益汇总</text>
					</view>
					<view class="title-line"></view>
				</view>

				<view class="summary-cards">
					<view class="summary-card income-card">
						<view class="card-header">
							<text class="card-icon">💵</text>
							<text class="card-title">总收入</text>
						</view>
						<view class="card-value">
							<text class="value-amount income-amount">¥{{ totalIncome.toFixed(2) }}</text>
						</view>
					</view>

					<view class="summary-card cost-card">
						<view class="card-header">
							<text class="card-icon">💸</text>
							<text class="card-title">总成本</text>
						</view>
						<view class="card-value">
							<text class="value-amount cost-amount">¥{{ totalCost.toFixed(2) }}</text>
						</view>
					</view>

					<view class="summary-card profit-card" :class="profit >= 0 ? 'positive-profit' : 'negative-profit'">
						<view class="card-header">
							<text class="card-icon">{{ profit >= 0 ? '📊' : '📉' }}</text>
							<text class="card-title">毛利润</text>
						</view>
						<view class="card-value">
							<text class="value-amount profit-amount">{{ profit >= 0 ? '+' : '' }}¥{{ profit.toFixed(2)
							}}</text>
							<text class="profit-rate">{{ profitMargin.toFixed(1) }}%</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 其他收入支出 -->
			<view class="section other-info">
				<view class="section-title">
					<view class="title-content">
						<text class="section-icon">📝</text>
						<text class="title-text">其他收入支出</text>
					</view>
					<view class="title-line"></view>
				</view>

				<view class="cost-grid">
					<view class="cost-item editable">
						<view class="cost-label">
							<text class="label-icon">💵</text>
							<text class="label">其他收入</text>
						</view>
						<view class="cost-value editable-value">
							<input class="value-input" type="number" v-model.number="otherIncome" placeholder="0.00"
								:min="0" :step="0.01" />
							<text class="currency">¥</text>
						</view>
					</view>

					<view class="cost-item editable">
						<view class="cost-label">
							<text class="label-icon">📝</text>
							<text class="label">其他支出</text>
						</view>
						<view class="cost-value editable-value">
							<input class="value-input" type="number" v-model.number="otherCost" placeholder="0.00"
								:min="0" :step="0.01" />
							<text class="currency">¥</text>
						</view>
					</view>
				</view>
			</view>
			<view class="section profit-summary">
				<view class="section-title">
					<view class="title-content">
						<text class="section-icon">📈</text>
						<text class="title-text">成本详情</text>
					</view>
					<view class="title-line"></view>
				</view>

				<view class="summary-cards">
					<view class="summary-card tea-picking-cost-card">
						<view class="card-header">
							<text class="card-icon">🍃</text>
							<text class="card-title">采茶工钱</text>
						</view>
						<view class="card-value">
							<text class="value-amount tea-picking-cost-amount">¥{{ teaPickingCost.toFixed(2) }}</text>
						</view>
					</view>

					<view class="summary-card labor-cost-card">
						<view class="card-header">
							<text class="card-icon">⏰</text>
							<text class="card-title">时工工钱</text>
						</view>
						<view class="card-value">
							<text class="value-amount labor-cost-amount">¥{{ hourlyLaborCost.toFixed(2) }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import SalesManager from '../../utils/salesManager.js'

export default {
	name: 'IncomeDetailPage',
	data() {
		return {
			// 日期信息（新版本：基于日期而不是单个工作记录）
			selectedDate: null,
			workRecords: [], // 该日期的所有工作记录

			// 销售模式相关
			salesMode: 'single_customer', // single_customer | multiple_customers

			// 销售记录列表（支持多个顾客）
			salesRecords: [
				{
					id: null,
					selling_price: 0,
					production: 0,
					customer_name: '',
					other_income: 0,
					other_cost: 0,
					notes: ''
				}
			],

			// 其他数据
			otherIncome: 0,
			otherCost: 0,
			salesManager: null,

			// 工人列表展开状态
			workersExpanded: false,

			// 防抖标志
			isToggling: false,

			// 产量分配相关（基于日期汇总）
			totalProduction: 0,
			morningProduction: 0,
			afternoonProduction: 0,

			// 模式切换相关
			showModeSelector: false,
			modeOptions: [
				{ value: 'single_customer', label: '单客户模式' },
				{ value: 'multiple_customers', label: '多客户模式' }
			]
		}
	},
	computed: {
		// 日总工钱（所有工人的工钱总和）
		dailyLaborCost() {
			if (this.workRecords && this.workRecords.length > 0) {
				// 新版本：基于多个工作记录计算
				const cost = this.workRecords.reduce((total, workRecord) => {
					const earnings = parseFloat(workRecord.total_earnings) || 0
					return total + earnings
				}, 0)
				console.log('📊 日总工钱计算:', {
					workRecordsCount: this.workRecords.length,
					dailyLaborCost: cost,
					workRecords: this.workRecords.map(wr => ({
						worker: wr.worker_name,
						earnings: parseFloat(wr.total_earnings) || 0
					}))
				})
				return cost
			} else if (this.workRecord) {
				// 向后兼容：基于单个工作记录计算
				const earnings = parseFloat(this.workRecord.total_earnings) || 0
				console.log('📊 单工作记录工钱:', earnings)
				return earnings
			}
			return 0
		},

		// 总销售收入（所有销售记录的收入之和）
		totalSalesIncome() {
			return this.salesRecords.reduce((total, record) => {
				const price = parseFloat(record.selling_price) || 0
				const production = parseFloat(record.production) || 0
				return total + (price * production)
			}, 0)
		},

		// 总收入（销售收入 + 其他收入）
		totalIncome() {
			const salesIncome = parseFloat(this.totalSalesIncome) || 0
			const otherIncome = parseFloat(this.otherIncome) || 0
			return salesIncome + otherIncome
		},

		// 采茶工钱（只包含采茶工作的成本）
		teaPickingCost() {
			if (this.workRecords && this.workRecords.length > 0) {
				const cost = this.workRecords.reduce((total, workRecord) => {
					if (workRecord.work_mode === 'tea_picking') {
						const earnings = parseFloat(workRecord.total_earnings) || 0
						return total + earnings
					}
					return total
				}, 0)
				console.log('🍃 采茶工钱计算:', {
					workRecordsCount: this.workRecords.length,
					teaPickingCost: cost,
					teaPickingRecords: this.workRecords.filter(wr => wr.work_mode === 'tea_picking').map(wr => ({
						worker: wr.worker_name,
						earnings: parseFloat(wr.total_earnings) || 0
					}))
				})
				return cost
			} else if (this.workRecord && this.workRecord.work_mode === 'tea_picking') {
				// 向后兼容：基于单个采茶工作记录计算
				const earnings = parseFloat(this.workRecord.total_earnings) || 0
				console.log('🍃 单采茶工作记录工钱:', earnings)
				return earnings
			}
			return 0
		},

		// 时工工钱（只包含时工工作的成本）
		hourlyLaborCost() {
			if (this.workRecords && this.workRecords.length > 0) {
				const cost = this.workRecords.reduce((total, workRecord) => {
					if (workRecord.work_mode === 'hourly') {
						const earnings = parseFloat(workRecord.total_earnings) || 0
						return total + earnings
					}
					return total
				}, 0)
				console.log('⏰ 时工工钱计算:', {
					workRecordsCount: this.workRecords.length,
					hourlyLaborCost: cost,
					hourlyRecords: this.workRecords.filter(wr => wr.work_mode === 'hourly').map(wr => ({
						worker: wr.worker_name,
						earnings: parseFloat(wr.total_earnings) || 0
					}))
				})
				return cost
			} else if (this.workRecord && this.workRecord.work_mode === 'hourly') {
				// 向后兼容：基于单个时工工作记录计算
				const earnings = parseFloat(this.workRecord.total_earnings) || 0
				console.log('⏰ 单时工工作记录工钱:', earnings)
				return earnings
			}
			return 0
		},

		// 总支出（新版本：支持日期汇总模式）
		totalCost() {
			// 使用分别计算的采茶成本和时工成本
			const teaPickingCost = parseFloat(this.teaPickingCost) || 0
			const hourlyLaborCost = parseFloat(this.hourlyLaborCost) || 0

			// 计算销售记录中的其他支出总和
			const salesOtherCost = this.salesRecords.reduce((total, record) => {
				const otherCost = parseFloat(record.other_cost) || 0
				return total + otherCost
			}, 0)

			// 计算全局其他支出
			const globalOtherCost = parseFloat(this.otherCost) || 0

			// 总支出 = 采茶工钱 + 时工工钱 + 销售记录其他支出 + 全局其他支出
			const totalCost = teaPickingCost + hourlyLaborCost + salesOtherCost + globalOtherCost

			console.log('💸 总支出计算明细:', {
				teaPickingCost: teaPickingCost,
				hourlyLaborCost: hourlyLaborCost,
				salesOtherCost: salesOtherCost,
				globalOtherCost: globalOtherCost,
				totalCost: totalCost
			})

			return totalCost
		},

		// 毛利润
		profit() {
			const totalIncome = parseFloat(this.totalIncome) || 0
			const totalCost = parseFloat(this.totalCost) || 0
			return totalIncome - totalCost
		},

		// 利润率
		profitMargin() {
			const totalIncome = parseFloat(this.totalIncome) || 0
			const profit = parseFloat(this.profit) || 0
			return totalIncome > 0 ? (profit / totalIncome) * 100 : 0
		},

		// 显示的工人列表（根据展开状态决定）
		displayedWorkers() {
			if (this.workersExpanded || this.workRecords.length <= 6) {
				return this.workRecords
			}
			return this.workRecords.slice(0, 6)
		},

		// 是否显示展开按钮
		showExpandButton() {
			return this.workRecords.length > 6
		},

		// 展开按钮文案
		expandButtonText() {
			if (this.workersExpanded) {
				return '收起'
			}
			return `展开全部(+${this.workRecords.length - 6}个)`
		},
	},
	onLoad(options) {
		console.log('详情页参数:', options)

		// 初始化销售管理器
		this.salesManager = new SalesManager()

		// 适配状态栏高度
		this.adaptStatusBar()

		// 监听工作记录更新事件
		console.log('🎧 [收入详情页] 注册workRecordUpdated事件监听器')
		uni.$on('updateIncomeRecord', this.handleWorkRecordUpdate)
		console.log('🎧 [收入详情页] 事件监听器注册完成')

		if (options.data) {
			try {
				const data = JSON.parse(decodeURIComponent(options.data))
				console.log('解析的数据:', data)

				// 新版本：设置日期和工作记录信息
				if (data.workRecords && data.date) {
					// 基于日期的数据（新版本）
					console.log('详情页接收到的数据:', {
						date: data.date,
						workRecords: data.workRecords?.length,
						dailyTotalProduction: data.dailyTotalProduction,
						dailyTotalCost: data.dailyTotalCost
					})

					this.selectedDate = data.date
					this.workRecords = data.workRecords
					this.totalProduction = data.dailyTotalProduction || 0

					console.log('设置后的产量和成本信息:', {
						totalProduction: this.totalProduction,
						workRecordsCount: this.workRecords?.length || 0,
						totalCost: this.totalCost // 这是计算属性，会自动计算
					})

					// 计算时间段产量（基于所有工作记录）
					this.calculateDailyTimePeriodProduction()
				} else if (data.workRecord) {
					// 向后兼容：基于单个工作记录的数据（旧版本）
					this.selectedDate = data.workRecord.date
					this.workRecords = [data.workRecord]
					this.totalProduction = this.salesManager.calculateWorkRecordProduction(data.workRecord)

					console.log('向后兼容模式设置:', {
						totalProduction: this.totalProduction,
						totalCost: this.totalCost // 计算属性会自动计算
					})

					// 计算时间段产量
					this.calculateDailyTimePeriodProduction()
				}

				// 设置销售记录和销售模式
				if (data.isPlaceholder) {
					// 新建销售记录（从占位符进入）
					console.log('检测到占位符记录，创建新的销售记录')

					// 初始化为单客户模式
					this.salesMode = 'single_customer'
					this.salesRecords = [{
						id: null,
						selling_price: 0,
						production: this.totalProduction, // 默认分配全部产量
						customer_name: '',
						other_income: 0,
						other_cost: 0,
						notes: ''
					}]
				} else if (data.isAggregated && data.salesRecords) {
					// 汇总记录：加载该日期的所有销售记录
					console.log('🎯 检测到汇总记录，加载所有销售记录:', data.salesRecords.length)
					console.log('📊 汇总记录详情:', data)
					console.log('📋 销售记录数组:', data.salesRecords)

					// 判断销售模式
					this.salesMode = data.salesRecords.length === 1 ? 'single_customer' : 'multiple_customers'

					this.salesRecords = data.salesRecords.map((record, index) => {
						console.log(`📝 处理客户 ${index + 1}:`, record)
						return {
							id: record.id,
							selling_price: record.selling_price || 0,
							production: record.production || 0,
							customer_name: record.customer_name || '',
							other_income: record.other_income || 0,
							other_cost: record.other_cost || 0,
							notes: record.notes || ''
						}
					})

					console.log('✅ 最终加载的销售记录数组:', this.salesRecords)
					console.log(`🎯 将显示 ${this.salesRecords.length} 个独立的客户卡片`)
				} else if (data.salesRecord && !data.salesRecord.id?.startsWith('placeholder_')) {
					// 编辑现有销售记录（非占位符）
					console.log('编辑现有销售记录:', data.salesRecord.id)

					// 单个记录编辑，设为单客户模式
					this.salesMode = 'single_customer'
					this.salesRecords = [{
						id: data.salesRecord.id,
						selling_price: data.salesRecord.selling_price || 0,
						production: data.salesRecord.production || 0,
						customer_name: data.salesRecord.customer_name || '',
						other_income: data.salesRecord.other_income || 0,
						other_cost: data.salesRecord.other_cost || 0,
						notes: data.salesRecord.notes || ''
					}]
				} else {
					// 加载该日期的现有销售记录
					console.log('加载该日期的现有销售记录')
					this.loadExistingSalesRecords()
				}

				// 设置其他收入和支出（汇总记录需要特殊处理）
				if (data.isAggregated && data.salesRecords) {
					// 汇总记录：计算所有销售记录的其他收入和支出总和
					this.otherIncome = data.salesRecords.reduce((sum, record) => sum + (parseFloat(record.other_income) || 0), 0)
					this.otherCost = data.salesRecords.reduce((sum, record) => sum + (parseFloat(record.other_cost) || 0), 0)
					console.log('汇总记录的其他收入/支出:', { otherIncome: this.otherIncome, otherCost: this.otherCost })
				} else {
					this.otherIncome = data.otherIncome || 0
					this.otherCost = data.otherCost || 0
				}

				console.log('初始化后的数据:', {
					workRecord: this.workRecord,
					salesRecords: this.salesRecords,
					totalProduction: this.totalProduction
				})
			} catch (e) {
				console.error('解析数据失败:', e)
				uni.showToast({
					title: '数据解析失败',
					icon: 'error'
				})
			}
		}
	},

	onShow() {
		// 页面显示时重新注册事件监听器（防止页面切换时监听器丢失）
		console.log('🎧 [收入详情页] 页面显示，重新注册事件监听器')
		uni.$off('updateIncomeRecord', this.handleWorkRecordUpdate) // 先移除避免重复
		uni.$on('updateIncomeRecord', this.handleWorkRecordUpdate)
		console.log('🎧 [收入详情页] 事件监听器重新注册完成')
	},

	onUnload() {
		// 移除事件监听
		console.log('🎧 [收入详情页] 页面卸载，移除事件监听器')
		uni.$off('updateIncomeRecord', this.handleWorkRecordUpdate)
	},

	methods: {
		// 切换工人列表展开状态
		toggleWorkersExpanded() {
			// 防抖处理，避免重复点击
			if (this.isToggling) {
				console.log('toggleWorkersExpanded: 正在处理中，忽略重复调用')
				return
			}

			this.isToggling = true

			try {
				console.log('toggleWorkersExpanded called')
				console.log('- 当前状态:', this.workersExpanded)
				console.log('- 工人总数:', this.workRecords.length)
				console.log('- 当前显示数:', this.displayedWorkers.length)

				this.workersExpanded = !this.workersExpanded

				console.log('- 新状态:', this.workersExpanded)
				console.log('- 新显示数:', this.displayedWorkers.length)

				// 显示用户反馈
				uni.showToast({
					title: this.workersExpanded ? '已展开全部工人' : '已收起工人列表',
					icon: 'none',
					duration: 1000
				})

				// 强制更新视图（uni-app有时需要）
				this.$nextTick(() => {
					this.$forceUpdate()
				})

			} catch (error) {
				console.error('toggleWorkersExpanded error:', error)
				uni.showToast({
					title: '操作失败',
					icon: 'error'
				})
			} finally {
				// 300ms后重置防抖标志
				setTimeout(() => {
					this.isToggling = false
				}, 300)
			}
		},

		// 计算单个工作记录的产量
		calculateWorkRecordProduction(workRecord) {
			if (!workRecord || workRecord.work_mode !== 'tea_picking') {
				return 0
			}

			if (this.salesManager) {
				return this.salesManager.calculateWorkRecordProduction(workRecord)
			}

			// 备用计算方法
			if (workRecord.tea_picking_details) {
				if (Array.isArray(workRecord.tea_picking_details)) {
					return workRecord.tea_picking_details.reduce((sum, detail) => {
						return sum + (parseFloat(detail.actual_weight) || 0)
					}, 0)
				} else if (typeof workRecord.tea_picking_details === 'object') {
					return parseFloat(workRecord.tea_picking_details.actual_weight) || 0
				}
			}

			return 0
		},

		// 添加销售记录（带智能自动填充，支持销售模式）
		addSalesRecord() {
			// 单客户模式不允许添加更多客户
			if (this.salesMode === 'single_customer') {
				uni.showToast({
					title: '单客户模式只能有一个客户',
					icon: 'none'
				})
				return
			}

			// 检查是否超过合理的客户数量限制
			if (this.salesRecords.length >= 5) {
				uni.showToast({
					title: '最多支持5个客户',
					icon: 'none'
				})
				return
			}

			// 添加新记录
			this.salesRecords.push({
				id: null,
				selling_price: 0,
				production: 0, // 先设为0，后面会自动填充
				customer_name: '',
				other_income: 0,
				other_cost: 0,
				notes: ''
			})

			// 触发智能自动填充逻辑
			this.applySmartAutoFill()

			// 显示提示
			const recordCount = this.salesRecords.length
			let message = `已添加第${recordCount}个客户`

			if (recordCount === 1) {
				message += '，已自动填入当日总产量'
			} else if (recordCount === 2) {
				message += '，已自动分配上午/下午产量'
			} else {
				const remainingProduction = Math.max(0, this.totalProduction - this.allocatedProduction + this.salesRecords[recordCount - 1].production)
				if (remainingProduction > 0) {
					message += `，已分配剩余产量 ${remainingProduction.toFixed(1)} 斤`
				} else {
					message += '，当前无剩余产量，请手动调整分配'
				}
			}

			uni.showToast({
				title: message,
				icon: 'none',
				duration: 2500
			})

			// 如果添加后产量超限，显示警告
			if (this.isOverAllocated) {
				setTimeout(() => {
					uni.showToast({
						title: '注意：总分配产量已超限',
						icon: 'none',
						duration: 2000
					})
				}, 2600)
			}
		},

		// 智能自动填充逻辑
		applySmartAutoFill() {
			const recordCount = this.salesRecords.length

			if (recordCount === 1) {
				// 第一条记录：自动填入当日总产量
				this.salesRecords[0].production = this.totalProduction
				console.log('智能填充：第一条记录填入总产量', this.totalProduction)

			} else if (recordCount === 2) {
				// 添加第二条记录后：重新分配前两条记录
				// 第一条：上午产量
				this.salesRecords[0].production = this.morningProduction
				// 第二条：下午产量
				this.salesRecords[1].production = this.afternoonProduction

				console.log('智能填充：重新分配前两条记录', {
					record1_morning: this.morningProduction,
					record2_afternoon: this.afternoonProduction
				})

			} else {
				// 第三条及以后：不自动填充，保持现有逻辑
				// 新记录默认分配剩余产量
				const newIndex = recordCount - 1
				this.salesRecords[newIndex].production = this.remainingProduction

				console.log('智能填充：第三条及以后记录分配剩余产量', this.remainingProduction)
			}

			// 确保所有产量值都是有效的数字并保留两位小数
			this.salesRecords.forEach((record) => {
				record.production = Math.max(0, parseFloat((record.production || 0).toFixed(2)))
			})
		},

		// 切换销售模式
		switchSalesMode(newMode) {
			if (this.salesMode === newMode) return

			// 检查是否有未保存的数据
			const hasUnsavedData = this.salesRecords.some(record =>
				record.customer_name || record.selling_price > 0 || record.production > 0
			)

			if (hasUnsavedData) {
				uni.showModal({
					title: '切换销售模式',
					content: '切换模式将清空当前销售数据，是否继续？',
					success: (res) => {
						if (res.confirm) {
							this.applySalesModeSwitch(newMode)
						}
					}
				})
			} else {
				this.applySalesModeSwitch(newMode)
			}
		},

		// 应用销售模式切换
		applySalesModeSwitch(newMode) {
			this.salesMode = newMode

			if (newMode === 'single_customer') {
				// 切换到单客户模式
				this.salesRecords = [{
					id: null,
					selling_price: 0,
					production: this.totalProduction,
					customer_name: '',
					other_income: 0,
					other_cost: 0,
					notes: ''
				}]
			} else {
				// 切换到多客户模式
				this.salesRecords = [
					{
						id: null,
						selling_price: 0,
						production: this.morningProduction,
						customer_name: '',
						other_income: 0,
						other_cost: 0,
						notes: ''
					},
					{
						id: null,
						selling_price: 0,
						production: this.afternoonProduction,
						customer_name: '',
						other_income: 0,
						other_cost: 0,
						notes: ''
					}
				]
			}

			uni.showToast({
				title: `已切换到${newMode === 'single_customer' ? '单客户' : '多客户'}模式`,
				icon: 'success'
			})
		},

		// 删除销售记录
		removeSalesRecord(index) {
			if (this.salesMode === 'single_customer') {
				uni.showToast({
					title: '单客户模式不能删除销售记录',
					icon: 'none'
				})
				return
			}

			if (this.salesRecords.length <= 1) {
				uni.showToast({
					title: '至少需要保留一个销售记录',
					icon: 'none'
				})
				return
			}

			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个销售记录吗？',
				success: (res) => {
					if (res.confirm) {
						this.salesRecords.splice(index, 1)

						// 删除后重新应用智能填充逻辑
						this.applySmartAutoFill()

						uni.showToast({
							title: '已删除并重新分配产量',
							icon: 'success'
						})
					}
				}
			})
		},





		// 计算基于日期的各时间段产量（新版本）
		calculateDailyTimePeriodProduction() {
			this.morningProduction = 0
			this.afternoonProduction = 0

			// 遍历该日期的所有工作记录
			this.workRecords.forEach(workRecord => {
				if (workRecord.work_mode === 'tea_picking' && workRecord.tea_picking_details) {
					// 处理数组格式的tea_picking_details
					if (Array.isArray(workRecord.tea_picking_details)) {
						workRecord.tea_picking_details.forEach(detail => {
							const actualWeight = parseFloat(detail.actual_weight) || 0
							let timePeriod = detail.time_period

							// 向后兼容：将历史数据中的 evening 和 noon 映射到 afternoon
							if (timePeriod === 'evening' || timePeriod === 'noon') {
								timePeriod = 'afternoon'
								console.log('⚠️ 检测到历史数据中的', detail.time_period, '时间段，已映射到afternoon')
							}

							switch (timePeriod) {
								case 'morning':
									this.morningProduction += actualWeight
									break
								case 'afternoon':
									this.afternoonProduction += actualWeight
									break
								default:
									// 未知时间段，默认归类到上午
									console.warn('⚠️ 未知时间段:', timePeriod, '已归类到morning')
									this.morningProduction += actualWeight
									break
							}
						})
					} else if (typeof workRecord.tea_picking_details === 'object') {
						// 处理单个对象格式的tea_picking_details（兼容旧数据）
						const detail = workRecord.tea_picking_details
						const actualWeight = parseFloat(detail.actual_weight) || 0
						let timePeriod = detail.time_period

						// 向后兼容：将历史数据中的 evening 和 noon 映射到 afternoon
						if (timePeriod === 'evening' || timePeriod === 'noon') {
							timePeriod = 'afternoon'
							console.log('⚠️ 检测到历史数据中的', detail.time_period, '时间段，已映射到afternoon')
						}

						switch (timePeriod) {
							case 'morning':
								this.morningProduction += actualWeight
								break
							case 'afternoon':
								this.afternoonProduction += actualWeight
								break
							default:
								// 未知时间段，默认归类到上午
								console.warn('⚠️ 未知时间段:', timePeriod, '已归类到morning')
								this.morningProduction += actualWeight
								break
						}
					}
				}
			})

			console.log('日期汇总时间段产量分配:', {
				date: this.selectedDate,
				morning: this.morningProduction,
				afternoon: this.afternoonProduction,
				total: this.totalProduction
			})
		},

		// 加载该日期的现有销售记录
		async loadExistingSalesRecords() {
			if (!this.selectedDate) return

			try {
				const existingSalesRecords = this.salesManager.salesDB.getSalesRecordsByDate(this.selectedDate)

				if (existingSalesRecords.length > 0) {
					// 有现有销售记录，加载它们并判断销售模式
					console.log('加载现有销售记录:', existingSalesRecords.length, '条')

					// 判断销售模式：检查是否有sales_mode字段，或根据记录数量判断
					const firstRecord = existingSalesRecords[0]
					if (firstRecord.sales_mode) {
						this.salesMode = firstRecord.sales_mode
					} else {
						// 向后兼容：根据记录数量判断
						this.salesMode = existingSalesRecords.length === 1 ? 'single_customer' : 'multiple_customers'
					}

					this.salesRecords = existingSalesRecords.map(record => ({
						id: record.id,
						selling_price: record.selling_price || 0,
						production: record.production || 0,
						customer_name: record.customer_name || '',
						other_income: record.other_income || 0,
						other_cost: record.other_cost || 0,
						notes: record.notes || ''
					}))

					console.log('设置销售模式为:', this.salesMode)
				} else {
					// 没有现有销售记录，创建默认记录（单客户模式）
					this.salesMode = 'single_customer'
					this.salesRecords = [{
						id: null,
						selling_price: 0,
						production: this.totalProduction, // 默认分配全部产量
						customer_name: '',
						other_income: 0,
						other_cost: 0,
						notes: ''
					}]
				}
			} catch (error) {
				console.error('加载现有销售记录失败:', error)
				// 创建默认记录（单客户模式）
				this.salesMode = 'single_customer'
				this.salesRecords = [{
					id: null,
					selling_price: 0,
					production: this.totalProduction,
					customer_name: '',
					other_income: 0,
					other_cost: 0,
					notes: ''
				}]
			}
		},

		// 🔧 新增：重新加载已保存的销售记录（用于产量变化时的自动重新分配）
		async reloadExistingSalesRecords() {
			if (!this.selectedDate) return

			try {
				console.log('🔄 [收入详情页] 重新加载已保存的销售记录')
				const existingSalesRecords = this.salesManager.salesDB.getSalesRecordsByDate(this.selectedDate)

				if (existingSalesRecords.length > 0) {
					// 有已保存的销售记录，保持客户信息和单价，只重置产量分配
					console.log('📋 [收入详情页] 找到已保存的销售记录:', existingSalesRecords.length, '条')

					this.salesRecords = existingSalesRecords.map((record, index) => {
						console.log(`📝 [收入详情页] 重新加载客户 ${index + 1}:`, {
							customer: record.customer_name,
							price: record.selling_price,
							originalProduction: record.production
						})

						return {
							id: record.id,
							selling_price: record.selling_price || 0, // 保持原有单价
							production: record.production || 0, // 保持原有产量分配（稍后会重新分配）
							customer_name: record.customer_name || '', // 保持原有客户名称
							other_income: record.other_income || 0,
							other_cost: record.other_cost || 0,
							notes: record.notes || ''
						}
					})

					console.log('✅ [收入详情页] 已保存销售记录重新加载完成，将进行产量重新分配')
				} else {
					// 没有已保存的销售记录，保持当前状态
					console.log('📋 [收入详情页] 未找到已保存的销售记录，保持当前状态')

					// 如果当前没有销售记录，创建默认记录
					if (this.salesRecords.length === 0) {
						this.salesRecords = [{
							id: null,
							selling_price: 0,
							production: this.totalProduction,
							customer_name: '',
							other_income: 0,
							other_cost: 0,
							notes: ''
						}]
						console.log('📝 [收入详情页] 创建默认销售记录')
					}
				}

			} catch (error) {
				console.error('❌ [收入详情页] 重新加载销售记录失败:', error)
			}
		},

		// 检查单个记录的产量是否超限
		isProductionOverLimit(production, index) {
			if (!production || production <= 0) return false

			// 计算除当前记录外的其他记录总产量
			const otherProduction = this.salesRecords.reduce((total, record, i) => {
				if (i !== index) {
					return total + (record.production || 0)
				}
				return total
			}, 0)

			// 检查当前产量 + 其他产量是否超过总产量
			return (production + otherProduction) > this.totalProduction
		},

		// 产量输入验证
		validateProductionInput(index) {
			const record = this.salesRecords[index]

			// 确保输入值为有效数字
			if (record.production < 0) {
				record.production = 0
			}

			// 实时检查是否超限
			if (this.isProductionOverLimit(record.production, index)) {
				// 自动调整到最大可分配产量
				const otherProduction = this.salesRecords.reduce((total, r, i) => {
					if (i !== index) {
						return total + (r.production || 0)
					}
					return total
				}, 0)

				const maxAllowed = Math.max(0, this.totalProduction - otherProduction)
				record.production = parseFloat(maxAllowed.toFixed(2))
			}
		},

		// 产量失焦验证
		validateProductionBlur(index) {
			const record = this.salesRecords[index]

			// 格式化数值
			if (record.production) {
				record.production = parseFloat(record.production.toFixed(2))
			} else {
				record.production = 0
			}

			// 最终验证
			this.validateProduction()
		},



		// 适配状态栏高度
		adaptStatusBar() {
			try {
				const systemInfo = uni.getSystemInfoSync()
				const statusBarHeight = systemInfo.statusBarHeight || 0

				console.log('状态栏高度:', statusBarHeight)

				// 设置 CSS 变量作为备用方案
				if (statusBarHeight > 0) {
					// #ifdef H5
					if (typeof document !== 'undefined') {
						document.documentElement.style.setProperty('--status-bar-height', `${statusBarHeight}px`)

						// 检查是否支持 safe-area-inset
						const supportsSafeArea = CSS.supports('padding-top', 'env(safe-area-inset-top)') ||
							CSS.supports('padding-top', 'constant(safe-area-inset-top)')

						if (!supportsSafeArea) {
							// 使用 JavaScript 动态设置
							this.$nextTick(() => {
								const detailContainer = document.querySelector('.detail-container')
								if (detailContainer) {
									detailContainer.style.paddingTop = `${statusBarHeight}px`
								}

								const header = document.querySelector('.header')
								if (header) {
									header.style.marginTop = `-${statusBarHeight}px`
									header.style.paddingTop = `${statusBarHeight + 15}px` // 15px 对应原来的 30rpx
								}
							})
						}
					}
					// #endif

					// #ifdef APP-PLUS || MP
					// 在 App 和小程序中，通过设置页面样式
					this.$nextTick(() => {
						const pages = getCurrentPages()
						const currentPage = pages[pages.length - 1]
						if (currentPage && currentPage.$vm) {
							currentPage.$vm.$el.style.paddingTop = `${statusBarHeight}px`
						}
					})
					// #endif
				}
			} catch (error) {
				console.log('状态栏适配失败:', error)
			}
		},



		// 验证产量分配是否合理（新版本）
		validateProductionAllocation() {
			const validation = this.allocationValidation

			if (!validation.isValid) {
				uni.showModal({
					title: '产量分配超限',
					content: `当前总分配产量 ${validation.allocated.toFixed(1)} 斤超出当日总产量 ${validation.total.toFixed(1)} 斤，超出 ${validation.excess.toFixed(1)} 斤。\n\n请调整各客户的产量分配后再保存。`,
					showCancel: false,
					confirmText: '我知道了'
				})
				return false
			}

			// 检查是否有负数产量
			const hasNegativeProduction = this.salesRecords.some(record => (record.production || 0) < 0)
			if (hasNegativeProduction) {
				uni.showModal({
					title: '产量数据错误',
					content: '产量不能为负数，请检查并修正。',
					showCancel: false,
					confirmText: '我知道了'
				})
				return false
			}

			// 检查是否有无效的产量数据
			const hasInvalidProduction = this.salesRecords.some(record => {
				const production = record.production
				return isNaN(production) || production === null || production === undefined
			})
			if (hasInvalidProduction) {
				uni.showModal({
					title: '产量数据错误',
					content: '存在无效的产量数据，请检查并修正。',
					showCancel: false,
					confirmText: '我知道了'
				})
				return false
			}

			return true
		},

		// 静默验证产量分配（用于自动保存，不显示弹窗）
		validateProductionAllocationSilent() {
			const validation = this.allocationValidation

			if (!validation.isValid) {
				console.log('⚠️ [收入详情页] 产量分配超限:', {
					allocated: validation.allocated,
					total: validation.total,
					excess: validation.excess
				})
				return false
			}

			// 检查是否有负数产量
			const hasNegativeProduction = this.salesRecords.some(record => (record.production || 0) < 0)
			if (hasNegativeProduction) {
				console.log('⚠️ [收入详情页] 存在负数产量')
				return false
			}

			// 检查是否有无效的产量数据
			const hasInvalidProduction = this.salesRecords.some(record => {
				const production = record.production
				return isNaN(production) || production === null || production === undefined
			})
			if (hasInvalidProduction) {
				console.log('⚠️ [收入详情页] 存在无效产量数据')
				return false
			}

			return true
		},

		// 返回上一页
		goBack() {
			uni.navigateBack()
		},



		// 保存记录（带二次确认）
		saveRecord() {
			// 数据验证（新版本：基于日期）
			if (!this.selectedDate) {
				uni.showToast({
					title: '工作日期缺失',
					icon: 'error'
				})
				return
			}

			if (!this.workRecords || this.workRecords.length === 0) {
				uni.showToast({
					title: '工作记录缺失',
					icon: 'error'
				})
				return
			}

			// 验证销售记录
			for (let i = 0; i < this.salesRecords.length; i++) {
				const record = this.salesRecords[i]

				if (!record.customer_name || record.customer_name.trim() === '') {
					uni.showToast({
						title: `请填写顾客${i + 1}的姓名`,
						icon: 'error'
					})
					return
				}

				if (!record.production || record.production <= 0) {
					uni.showToast({
						title: `顾客${i + 1}的产量无效`,
						icon: 'error'
					})
					return
				}

				if (!record.selling_price || record.selling_price <= 0) {
					uni.showToast({
						title: `顾客${i + 1}的单价无效`,
						icon: 'error'
					})
					return
				}
			}

			// 验证产量分配（新版本：移除严格限制，允许超限但给出警告）
			if (!this.validateProductionAllocation()) {
				return
			}

			// 检查是否有空的客户姓名（其他字段已在上面验证过）
			const emptyNameRecords = this.salesRecords.filter(record =>
				!record.customer_name || record.customer_name.trim() === ''
			)

			if (emptyNameRecords.length > 0) {
				uni.showModal({
					title: '客户信息不完整',
					content: `有${emptyNameRecords.length}个销售记录的客户姓名为空，请填写后再保存。`,
					showCancel: false,
					confirmText: '我知道了'
				})
				return
			}

			// 数据验证通过后，显示确认对话框
			this.showSaveConfirmDialog()
		},

		// 显示保存确认对话框
		showSaveConfirmDialog() {
			// 构建数据预览内容
			const previewData = this.buildDataPreview()

			uni.showModal({
				title: '确认保存',
				content: previewData,
				confirmText: '确认保存',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						// 用户确认保存
						this.performSave()
					}
					// 用户取消则不执行任何操作
				}
			})
		},

		// 构建数据预览内容
		buildDataPreview() {
			let preview = '请确认以下信息：\n'

			// 销售记录信息
			preview += `🛒 销售记录 (${this.salesRecords.length}个顾客)：\n`
			this.salesRecords.forEach((record, index) => {
				const subtotal = (record.selling_price || 0) * (record.production || 0)
				preview += `  顾客${index + 1}：\n`
				preview += `    销售产量：${(record.production || 0).toFixed(2)} 斤\n`
				preview += `    单价：¥${(record.selling_price || 0).toFixed(2)}/斤\n`
				preview += `    小计：¥${subtotal.toFixed(2)}\n`
				if (record.customer_name && record.customer_name.trim()) {
					preview += `    顾客：${record.customer_name.trim()}\n`
				}
				preview += '\n'
			})

			// 汇总信息
			preview += `💵 销售总收入：¥${this.totalSalesIncome.toFixed(2)}\n\n`

			if (this.otherIncome && this.otherIncome > 0) {
				preview += `💰 其他收入：¥${this.otherIncome.toFixed(2)}\n`
			}

			preview += `💵 总收入：¥${this.totalIncome.toFixed(2)}\n`
			preview += `💸 总支出：¥${this.totalCost.toFixed(2)}\n`
			preview += `💰 毛利润：¥${this.profit.toFixed(2)}\n`

			return preview
		},

		// 执行实际保存操作
		async performSave() {
			try {
				console.log('🚀 开始保存操作')
				console.log('当前状态:', {
					selectedDate: this.selectedDate,
					salesMode: this.salesMode,
					totalProduction: this.totalProduction,
					salesRecordsCount: this.salesRecords.length,
					allocatedProduction: this.allocatedProduction,
					salesManager: !!this.salesManager,
					salesRecords: this.salesRecords
				})

				// 1. 基础数据验证
				if (!this.selectedDate) {
					throw new Error('工作日期缺失，请重新进入页面')
				}

				if (!this.salesManager) {
					throw new Error('销售管理器未初始化，请重新进入页面')
				}

				if (!this.salesRecords || this.salesRecords.length === 0) {
					throw new Error('销售记录为空，请添加客户信息')
				}

				// 2. 销售模式特定验证
				if (this.salesMode === 'single_customer') {
					const record = this.salesRecords[0]
					if (!record.customer_name || record.customer_name.trim() === '') {
						throw new Error('请填写客户姓名')
					}
					if (!record.selling_price || parseFloat(record.selling_price) <= 0) {
						throw new Error('销售单价必须大于0')
					}
					if (!record.production || parseFloat(record.production) <= 0) {
						throw new Error('产量必须大于0')
					}
				} else if (this.salesMode === 'multiple_customers') {
					for (let i = 0; i < this.salesRecords.length; i++) {
						const record = this.salesRecords[i]
						if (!record.customer_name || record.customer_name.trim() === '') {
							throw new Error(`请填写第${i + 1}个客户的姓名`)
						}
						if (!record.selling_price || parseFloat(record.selling_price) <= 0) {
							throw new Error(`第${i + 1}个客户的销售单价必须大于0`)
						}
						if (!record.production || parseFloat(record.production) <= 0) {
							throw new Error(`第${i + 1}个客户的产量必须大于0`)
						}
					}
				}

				// 3. 产量分配验证
				const validation = this.allocationValidation
				if (!validation.isValid) {
					throw new Error(`产量分配超限：${validation.message}`)
				}

				console.log('✅ 数据验证通过')

				// 4. 执行保存操作
				let result
				if (this.salesMode === 'single_customer') {
					console.log('💾 保存单客户记录')
					result = await this.saveSingleCustomerRecord()
				} else {
					console.log('💾 保存多客户记录')
					result = await this.saveMultipleCustomersRecord()
				}

				console.log('保存结果:', result)

				if (result && result.success) {
					console.log('✅ 保存成功')

					// 触发保存事件，通知收入页面更新
					uni.$emit('updateIncomeRecord', {
						date: this.selectedDate,
						salesMode: this.salesMode,
						workRecords: this.workRecords,
						salesRecords: Array.isArray(result.data) ? result.data : [result.data],
						dailyTotalProduction: this.totalProduction,
						dailyTotalCost: this.totalCost,
						totalIncome: this.totalIncome
					})

					uni.showToast({
						title: result.message || '保存成功',
						icon: 'success'
					})

					// 延迟返回，让用户看到成功提示
					setTimeout(() => {
						this.goBack()
					}, 1500)
				} else {
					throw new Error(result?.message || '保存操作返回失败结果')
				}

			} catch (error) {
				console.error('❌ 保存失败:', error)
				console.error('错误堆栈:', error.stack)

				// 显示详细错误信息
				uni.showModal({
					title: '保存失败',
					content: `${error.message}\n\n请检查输入数据是否正确。`,
					showCancel: false,
					confirmText: '确定'
				})
			}
		},

		// 保存单客户记录
		async saveSingleCustomerRecord() {
			try {
				const record = this.salesRecords[0]

				console.log('📝 准备保存单客户记录:', record)

				// 检查是否为占位符ID
				let recordId = record.id
				if (recordId && recordId.startsWith('placeholder_')) {
					recordId = null
					console.log('🔄 检测到占位符ID，将创建新记录')
				}

				// 数据预处理和验证
				const customerData = {
					id: recordId,
					customer_name: (record.customer_name || '').trim(),
					selling_price: parseFloat(record.selling_price) || 0,
					production: parseFloat(record.production) || 0,
					other_income: parseFloat(this.otherIncome) || 0,
					other_cost: parseFloat(this.otherCost) || 0,
					notes: (record.notes || '').trim()
				}

				console.log('🔄 处理后的客户数据:', customerData)

				// 最后一次数据验证
				if (!customerData.customer_name) {
					throw new Error('客户姓名不能为空')
				}
				if (customerData.selling_price <= 0) {
					throw new Error('销售单价必须大于0')
				}
				if (customerData.production <= 0) {
					throw new Error('产量必须大于0')
				}

				// 🔧 修复：直接使用 createOrUpdateSalesRecord 以确保正确处理现有记录ID
				console.log('📞 调用销售管理器保存记录')

				const salesData = {
					id: customerData.id,
					date: this.selectedDate,
					selling_price: customerData.selling_price,
					production: customerData.production,
					other_income: customerData.other_income,
					other_cost: customerData.other_cost,
					customer_name: customerData.customer_name,
					notes: customerData.notes
				}

				console.log('💾 [收入详情页] 单客户保存数据:', salesData)
				console.log('🔧 [收入详情页] 操作类型:', salesData.id ? '更新现有记录' : '创建新记录')

				const result = await this.salesManager.createOrUpdateSalesRecord(salesData)

				console.log('💾 销售管理器返回结果:', result)

				if (!result) {
					throw new Error('销售管理器返回空结果')
				}

				return result

			} catch (error) {
				console.error('❌ 保存单客户记录异常:', error)

				// 根据错误类型提供不同的错误信息
				let errorMessage = '保存失败'

				if (error.message.includes('验证') || error.message.includes('不能为空') || error.message.includes('必须大于')) {
					errorMessage = `数据验证失败：${error.message}`
				} else if (error.message.includes('数据库')) {
					errorMessage = `数据库操作失败：${error.message}`
				} else if (error.message.includes('网络')) {
					errorMessage = `网络连接失败：${error.message}`
				} else {
					errorMessage = `系统错误：${error.message}`
				}

				return {
					success: false,
					message: errorMessage,
					error: error
				}
			}
		},

		// 保存多客户记录
		async saveMultipleCustomersRecord() {
			try {
				console.log('💾 [收入详情页] 开始保存多客户记录')

				// 🔧 修复：获取现有的销售记录以便更新而不是创建新记录
				const existingSalesRecords = this.salesManager.salesDB.getSalesRecordsByDate(this.selectedDate)
				console.log('🔍 [收入详情页] 找到现有销售记录:', existingSalesRecords.length, '条')

				const savedRecords = []

				// 🔧 修复：逐个保存每个客户记录，正确处理现有记录ID
				for (let i = 0; i < this.salesRecords.length; i++) {
					const record = this.salesRecords[i]

					// 🔧 修复：查找对应的现有记录
					let existingRecordId = null
					if (existingSalesRecords.length > i) {
						existingRecordId = existingSalesRecords[i].id
						console.log(`🔄 [收入详情页] 客户 ${i + 1} 将更新现有记录:`, existingRecordId)
					} else {
						console.log(`➕ [收入详情页] 客户 ${i + 1} 将创建新记录`)
					}

					// 检查是否为占位符ID，如果是则设为null以创建新记录
					let recordId = record.id
					if (recordId && recordId.startsWith('placeholder_')) {
						recordId = null
						console.log(`🔧 [收入详情页] 检测到占位符ID ${record.id}，将创建新记录`)
					} else if (!recordId && existingRecordId) {
						recordId = existingRecordId
					}

					// 其他收入和支出分配逻辑：只在第一个记录中保存全局的其他收入和支出
					const otherIncomeForRecord = (i === 0) ? (parseFloat(this.otherIncome) || 0) : 0
					const otherCostForRecord = (i === 0) ? (parseFloat(this.otherCost) || 0) : 0

					const salesData = {
						id: recordId,
						date: this.selectedDate,
						selling_price: parseFloat(record.selling_price) || 0,
						production: parseFloat(record.production) || 0,
						other_income: otherIncomeForRecord,
						other_cost: otherCostForRecord,
						customer_name: record.customer_name || '',
						notes: record.notes || ''
					}

					console.log(`💾 [收入详情页] 准备保存客户 ${i + 1} 记录:`, salesData)
					console.log(`🔧 [收入详情页] 操作类型: ${salesData.id ? '更新现有记录' : '创建新记录'}`)

					const result = await this.salesManager.createOrUpdateSalesRecord(salesData)

					console.log(`💾 [收入详情页] 客户 ${i + 1} 保存结果:`, result)

					if (result.success) {
						savedRecords.push(result.data)
						console.log(`✅ [收入详情页] 客户 ${i + 1} 保存成功`)
					} else {
						console.error(`❌ [收入详情页] 客户 ${i + 1} 保存失败:`, result.message)
						throw new Error(`保存客户${i + 1}的记录失败: ${result.message}`)
					}
				}

				console.log('✅ [收入详情页] 多客户记录保存完成，共保存', savedRecords.length, '条记录')

				return {
					success: true,
					data: savedRecords,
					message: `成功保存${savedRecords.length}条客户记录`
				}

			} catch (error) {
				console.error('❌ [收入详情页] 保存多客户记录失败:', error)
				return {
					success: false,
					message: error.message || '保存多客户记录失败'
				}
			}
		},

		// 旧的保存方法（保留作为备用）
		async performSaveLegacy() {
			try {
				console.log('开始保存操作，当前状态:', {
					selectedDate: this.selectedDate,
					totalProduction: this.totalProduction,
					salesRecordsCount: this.salesRecords.length,
					allocatedProduction: this.allocatedProduction
				})

				const savedRecords = []

				// 保存每个销售记录
				for (let i = 0; i < this.salesRecords.length; i++) {
					const record = this.salesRecords[i]

					// 检查是否为占位符ID，如果是则设为null以创建新记录
					let recordId = record.id
					if (recordId && recordId.startsWith('placeholder_')) {
						recordId = null
						console.log(`检测到占位符ID ${record.id}，将创建新记录`)
					}

					// 其他收入和支出分配逻辑：只在第一个记录中保存全局的其他收入和支出
					const otherIncomeForRecord = (i === 0) ? (this.otherIncome || 0) : 0
					const otherCostForRecord = (i === 0) ? (this.otherCost || 0) : 0

					const salesData = {
						id: recordId,
						date: this.selectedDate, // 新版本：使用日期而不是工作记录ID
						selling_price: parseFloat(record.selling_price) || 0,
						production: parseFloat(record.production) || 0,
						other_income: otherIncomeForRecord,
						other_cost: otherCostForRecord,
						customer_name: record.customer_name || '',
						notes: record.notes || ''
					}

					console.log(`准备保存销售记录 ${i + 1}:`, salesData)
					console.log(`操作类型: ${salesData.id ? '更新现有记录' : '创建新记录'}`)

					const result = await this.salesManager.createOrUpdateSalesRecord(salesData)

					console.log(`销售记录 ${i + 1} 保存结果:`, result)

					if (result.success) {
						savedRecords.push(result.data)
						console.log(`✅ 销售记录 ${i + 1} 保存成功`)
					} else {
						console.error(`❌ 销售记录 ${i + 1} 保存失败:`, result.message)
						throw new Error(`保存顾客${i + 1}的记录失败: ${result.message}`)
					}
				}

				// 触发保存事件，通知收入页面更新（新版本：基于日期）
				uni.$emit('updateIncomeRecord', {
					date: this.selectedDate,
					workRecords: this.workRecords,
					salesRecords: savedRecords,
					dailyTotalProduction: this.totalProduction,
					dailyTotalCost: this.totalCost,
					totalIncome: this.totalIncome
				})

				uni.showToast({
					title: `成功保存${savedRecords.length}条销售记录`,
					icon: 'success'
				})

				// 延迟返回，让用户看到成功提示
				setTimeout(() => {
					this.goBack()
				}, 1500)

			} catch (error) {
				console.error('保存销售记录失败:', error)
				uni.showToast({
					title: '保存失败: ' + error.message,
					icon: 'error'
				})
			}
		},

		// 处理工作记录更新事件
		async handleWorkRecordUpdate(eventData) {
			console.log('🔄 [收入详情页] 收到工作记录更新事件:', eventData)

			// 检查是否是当前日期的更新
			if (eventData.type === 'workRecordUpdated' && eventData.updatedRecord) {
				const updatedRecord = eventData.updatedRecord

				// 检查是否是当前页面相关的记录
				if (updatedRecord.date === this.selectedDate) {
					console.log('🎯 [收入详情页] 检测到当前日期的工作记录更新')

					// 检查是否有产量变化
					if (eventData.productionChanged) {
						console.log('📊 [收入详情页] 检测到产量变化，触发自动重新分配')
						console.log('📊 [收入详情页] 原产量:', eventData.originalProduction)
						console.log('📊 [收入详情页] 新产量:', eventData.newProduction)

						// 重新计算总产量
						await this.recalculateProductionAndReallocate()

						// 显示提示
						uni.showToast({
							title: '产量已更新，已自动重新分配',
							icon: 'success',
							duration: 2000
						})
					} else if (eventData.earningsChanged) {
						console.log('💰 [收入详情页] 检测到收入变化，更新成本信息')

						// 重新计算成本
						this.calculateDailyTimePeriodProduction()

						// 显示提示
						uni.showToast({
							title: '工作记录已更新',
							icon: 'success',
							duration: 1500
						})
					}
				}
			} else if (eventData.type === 'workRecordDeleted' && eventData.deletedRecord) {
				// 🔧 新增：处理工作记录删除事件
				const deletedRecord = eventData.deletedRecord

				// 检查是否是当前日期的记录
				if (deletedRecord.date === this.selectedDate) {
					console.log('🗑️ [收入详情页] 检测到当前日期的工作记录被删除')
					console.log('🗑️ [收入详情页] 级联删除的销售记录数量:', eventData.cascadeDeletedSalesRecords ? eventData.cascadeDeletedSalesRecords.length : 0)

					// 显示提示并返回上一页
					uni.showModal({
						title: '工作记录已删除',
						content: `该日期的工作记录已被删除${eventData.cascadeDeletedSalesRecords && eventData.cascadeDeletedSalesRecords.length > 0 ? '，相关销售记录也已清理' : ''}。\n\n将返回收入列表页面。`,
						showCancel: false,
						confirmText: '确定',
						success: () => {
							// 返回上一页
							uni.navigateBack()
						}
					})
				}
			}
		},

		// 重新计算产量并自动分配
		async recalculateProductionAndReallocate() {
			try {
				console.log('🔄 [收入详情页] 开始重新计算产量和分配')

				// 保存当前的时间段产量用于对比
				const previousTimePeriodProduction = {
					morning: this.morningProduction,
					afternoon: this.afternoonProduction,
					total: this.totalProduction
				}

				// 重新获取当日的工作记录
				const workRecords = uni.getStorageSync('workRecords') || []
				const dateWorkRecords = workRecords.filter(record => record.date === this.selectedDate)

				if (dateWorkRecords.length === 0) {
					console.log('⚠️ [收入详情页] 未找到当日工作记录')
					return
				}

				// 更新工作记录数据
				this.workRecords = dateWorkRecords

				// 重新计算总产量
				this.totalProduction = dateWorkRecords.reduce((total, record) => {
					if (record.work_mode === 'tea_picking') {
						return total + this.calculateWorkRecordProduction(record)
					}
					return total
				}, 0)

				console.log('📊 [收入详情页] 重新计算的总产量:', this.totalProduction)

				// 重新计算时间段产量
				this.calculateDailyTimePeriodProduction()

				// 🔧 修复：重新加载已保存的销售记录（如果存在）
				console.log('🔄 [收入详情页] 重新加载已保存的销售记录')
				await this.reloadExistingSalesRecords()

				// 检测时间段产量变化
				const timePeriodChanges = this.detectTimePeriodChanges(previousTimePeriodProduction)
				console.log('📊 [收入详情页] 时间段产量变化:', timePeriodChanges)

				// 根据客户数量和时间段变化进行智能重新分配
				await this.applySmartReallocationWithChanges(timePeriodChanges)

				// 等待一小段时间确保分配完成
				await new Promise(resolve => setTimeout(resolve, 200))

				// 🔧 修复：自动保存重新分配后的销售记录
				console.log('💾 [收入详情页] 开始自动保存重新分配的销售记录')
				await this.performAutoSave()

				console.log('✅ [收入详情页] 产量重新计算、分配和保存完成')

			} catch (error) {
				console.error('❌ [收入详情页] 重新计算产量失败:', error)
			}
		},

		// 检测时间段产量变化
		detectTimePeriodChanges(previousProduction) {
			const changes = {
				morning: this.morningProduction - previousProduction.morning,
				afternoon: this.afternoonProduction - previousProduction.afternoon,
				total: this.totalProduction - previousProduction.total
			}

			// 标记哪些时间段有显著变化（大于0.01）
			changes.morningChanged = Math.abs(changes.morning) > 0.01
			changes.afternoonChanged = Math.abs(changes.afternoon) > 0.01
			changes.totalChanged = Math.abs(changes.total) > 0.01

			return changes
		},

		// 基于时间段变化的智能重新分配
		async applySmartReallocationWithChanges(timePeriodChanges) {
			const customerCount = this.salesRecords.length
			console.log('🎯 [收入详情页] 开始基于时间段变化的智能重新分配')
			console.log('🎯 [收入详情页] 客户数量:', customerCount)
			console.log('🎯 [收入详情页] 时间段变化:', timePeriodChanges)

			// 🔧 改进：检查是否有已保存的客户信息
			const hasExistingCustomers = this.salesRecords.some(record => record.customer_name && record.customer_name.trim() !== '')
			console.log('🎯 [收入详情页] 是否有已保存的客户信息:', hasExistingCustomers)

			if (customerCount === 0) {
				// 没有销售记录，创建默认记录
				console.log('📊 [收入详情页] 没有销售记录，创建默认记录')
				this.salesRecords = [{
					id: null,
					selling_price: 0,
					production: this.totalProduction,
					customer_name: '',
					other_income: 0,
					other_cost: 0,
					notes: ''
				}]
				return
			}

			if (customerCount === 1) {
				// 单客户：分配全部产量
				this.salesRecords[0].production = this.totalProduction
				console.log('📊 [收入详情页] 单客户分配全部产量:', this.totalProduction)

			} else if (customerCount === 2) {
				// 🔧 双客户：根据时间段变化智能分配
				console.log('📊 [收入详情页] 双客户场景，基于时间段变化进行智能分配')

				if (timePeriodChanges.morningChanged || timePeriodChanges.afternoonChanged) {
					// 有具体时间段变化，精确分配
					console.log('📊 [收入详情页] 检测到时间段产量变化，进行精确分配')

					if (hasExistingCustomers) {
						// 🔧 改进：有已保存的客户，使用增量调整策略
						console.log('📊 [收入详情页] 检测到已保存客户，使用增量调整策略')

						if (timePeriodChanges.morningChanged) {
							// 上午产量变化，调整第一个客户
							const change = timePeriodChanges.morning
							this.salesRecords[0].production = Math.max(0, (this.salesRecords[0].production || 0) + change)
							console.log(`📊 [收入详情页] 上午产量变化 ${change}，调整第一个客户产量到: ${this.salesRecords[0].production}`)
						}

						if (timePeriodChanges.afternoonChanged) {
							// 下午产量变化，调整第二个客户
							const afternoonChange = timePeriodChanges.afternoon

							this.salesRecords[1].production = Math.max(0, (this.salesRecords[1].production || 0) + afternoonChange)
							console.log(`📊 [收入详情页] 下午产量变化 ${afternoonChange}，调整第二个客户产量到: ${this.salesRecords[1].production}`)
						}
					} else {
						// 🔧 改进：没有已保存的客户，使用时间段直接分配策略
						console.log('📊 [收入详情页] 没有已保存客户，使用时间段直接分配策略')
						this.salesRecords[0].production = this.morningProduction
						this.salesRecords[1].production = this.afternoonProduction
						console.log('📊 [收入详情页] 时间段直接分配:', {
							customer1_morning: this.salesRecords[0].production,
							customer2_afternoon: this.salesRecords[1].production
						})
					}
				} else {
					// 没有具体时间段变化，使用原有的智能分配逻辑
					console.log('📊 [收入详情页] 无具体时间段变化，使用通用智能分配')
					await this.applySmartReallocation()
					return
				}

			} else {
				// 多客户：使用原有的比例分配逻辑
				console.log('📊 [收入详情页] 多客户场景，使用比例分配')
				await this.applySmartReallocation()
				return
			}

			// 确保所有产量值都是有效的数字
			this.salesRecords.forEach((record) => {
				record.production = Math.max(0, parseFloat((record.production || 0).toFixed(2)))
			})

			// 验证分配结果
			const finalAllocatedTotal = this.salesRecords.reduce((sum, record) => sum + (record.production || 0), 0)
			console.log('✅ [收入详情页] 基于时间段变化的智能分配完成')
			console.log('✅ [收入详情页] 分配验证:', {
				totalProduction: this.totalProduction,
				allocatedTotal: finalAllocatedTotal,
				difference: Math.abs(this.totalProduction - finalAllocatedTotal)
			})

			// 如果分配总量与实际总量有差异，调整最后一个客户的产量
			const difference = this.totalProduction - finalAllocatedTotal
			if (Math.abs(difference) > 0.01 && this.salesRecords.length > 0) {
				const lastIndex = this.salesRecords.length - 1
				this.salesRecords[lastIndex].production = Math.max(0, (this.salesRecords[lastIndex].production || 0) + difference)
				console.log('🔧 [收入详情页] 调整最后一个客户产量以匹配总产量:', difference)
			}
		},

		// 智能重新分配逻辑
		async applySmartReallocation() {
			const customerCount = this.salesRecords.length
			console.log('🎯 [收入详情页] 开始智能重新分配，客户数量:', customerCount)
			console.log('🎯 [收入详情页] 当前总产量:', this.totalProduction)
			console.log('🎯 [收入详情页] 时间段产量分布:', {
				morning: this.morningProduction,
				afternoon: this.afternoonProduction
			})

			// 获取当前已分配的总产量
			const currentAllocatedTotal = this.salesRecords.reduce((sum, record) => sum + (record.production || 0), 0)
			console.log('🎯 [收入详情页] 当前已分配总产量:', currentAllocatedTotal)

			if (customerCount === 1) {
				// 单客户：分配全部产量
				this.salesRecords[0].production = this.totalProduction
				console.log('📊 [收入详情页] 单客户分配全部产量:', this.totalProduction)

			} else if (customerCount === 2) {
				// 🔧 修复：双客户智能分配逻辑
				console.log('📊 [收入详情页] 双客户场景，开始智能分配')

				// 检查是否有明确的时间段产量分布
				const hasTimePeriodData = this.morningProduction > 0 || this.afternoonProduction > 0

				if (hasTimePeriodData) {
					// 有时间段数据，按时间段分配
					console.log('📊 [收入详情页] 检测到时间段产量数据，按时间段分配')

					// 第一客户：上午产量，第二客户：下午产量
					this.salesRecords[0].production = this.morningProduction
					this.salesRecords[1].production = this.afternoonProduction

					console.log('📊 [收入详情页] 时间段分配结果:', {
						customer1_morning: this.morningProduction,
						customer2_afternoon: this.afternoonProduction
					})
				} else {
					// 没有时间段数据，智能分配
					console.log('📊 [收入详情页] 无时间段数据，使用智能分配策略')

					if (currentAllocatedTotal > 0) {
						// 如果已有分配，计算产量变化并智能调整
						const productionDifference = this.totalProduction - currentAllocatedTotal
						console.log('📊 [收入详情页] 产量变化:', productionDifference)

						if (Math.abs(productionDifference) > 0.01) {
							// 有产量变化，智能分配变化量
							if (productionDifference > 0) {
								// 产量增加：优先分配给第一个客户
								this.salesRecords[0].production = (this.salesRecords[0].production || 0) + productionDifference
								console.log('📊 [收入详情页] 产量增加，分配给第一个客户:', productionDifference)
							} else {
								// 产量减少：从第一个客户开始减少
								const reductionAmount = Math.abs(productionDifference)
								const customer1Current = this.salesRecords[0].production || 0

								if (customer1Current >= reductionAmount) {
									// 第一个客户的产量足够减少
									this.salesRecords[0].production = customer1Current - reductionAmount
									console.log('📊 [收入详情页] 产量减少，从第一个客户减少:', reductionAmount)
								} else {
									// 第一个客户产量不够，需要从两个客户都减少
									const remainingReduction = reductionAmount - customer1Current
									this.salesRecords[0].production = 0
									this.salesRecords[1].production = Math.max(0, (this.salesRecords[1].production || 0) - remainingReduction)
									console.log('📊 [收入详情页] 产量减少，两个客户都减少:', {
										customer1_reduction: customer1Current,
										customer2_reduction: remainingReduction
									})
								}
							}
						}
					} else {
						// 没有现有分配，平均分配
						const averageProduction = parseFloat((this.totalProduction / 2).toFixed(2))
						this.salesRecords[0].production = averageProduction
						this.salesRecords[1].production = this.totalProduction - averageProduction // 确保总和准确
						console.log('📊 [收入详情页] 无现有分配，平均分配:', averageProduction)
					}
				}

			} else {
				// 多客户：保持现有分配比例，但调整到新的总产量
				console.log('📊 [收入详情页] 多客户场景，按比例重新分配')

				if (currentAllocatedTotal > 0) {
					// 按比例重新分配
					this.salesRecords.forEach((record, index) => {
						const ratio = (record.production || 0) / currentAllocatedTotal
						record.production = parseFloat((this.totalProduction * ratio).toFixed(2))
						console.log(`📊 [收入详情页] 客户${index + 1}按比例分配:`, record.production)
					})
				} else {
					// 如果当前总产量为0，平均分配
					const averageProduction = parseFloat((this.totalProduction / customerCount).toFixed(2))
					this.salesRecords.forEach((record, index) => {
						record.production = index === customerCount - 1
							? this.totalProduction - (averageProduction * (customerCount - 1)) // 最后一个客户分配剩余产量
							: averageProduction
						console.log(`📊 [收入详情页] 客户${index + 1}平均分配:`, record.production)
					})
				}
			}

			// 确保所有产量值都是有效的数字
			this.salesRecords.forEach((record) => {
				record.production = Math.max(0, parseFloat((record.production || 0).toFixed(2)))
			})

			// 验证分配结果
			const finalAllocatedTotal = this.salesRecords.reduce((sum, record) => sum + (record.production || 0), 0)
			console.log('✅ [收入详情页] 智能重新分配完成')
			console.log('✅ [收入详情页] 分配验证:', {
				totalProduction: this.totalProduction,
				allocatedTotal: finalAllocatedTotal,
				difference: Math.abs(this.totalProduction - finalAllocatedTotal)
			})

			// 如果分配总量与实际总量有差异，调整最后一个客户的产量
			const difference = this.totalProduction - finalAllocatedTotal
			if (Math.abs(difference) > 0.01 && this.salesRecords.length > 0) {
				const lastIndex = this.salesRecords.length - 1
				this.salesRecords[lastIndex].production = Math.max(0, (this.salesRecords[lastIndex].production || 0) + difference)
				console.log('🔧 [收入详情页] 调整最后一个客户产量以匹配总产量:', difference)
			}
		},

		// 自动保存销售记录（无需用户确认）
		async performAutoSave() {
			try {
				console.log('💾 [收入详情页] 开始自动保存操作')

				// 数据验证
				if (!this.selectedDate) {
					console.log('❌ [收入详情页] 自动保存失败：缺少日期信息')
					return
				}

				if (!this.salesRecords || this.salesRecords.length === 0) {
					console.log('❌ [收入详情页] 自动保存失败：没有销售记录')
					return
				}

				// 验证产量分配（静默验证，不显示弹窗）
				const isValidAllocation = this.validateProductionAllocationSilent()
				if (!isValidAllocation) {
					console.log('❌ [收入详情页] 自动保存失败：产量分配验证失败')
					return
				}

				const savedRecords = []

				// 保存每个销售记录
				for (let i = 0; i < this.salesRecords.length; i++) {
					const record = this.salesRecords[i]

					// 🔧 修复：在自动保存时，只跳过完全空的记录（没有客户名称且没有产量）
					// 如果有产量但没有客户名称，可能是重新分配过程中的临时状态，仍需保存
					if ((!record.customer_name || record.customer_name.trim() === '') &&
						(!record.production || record.production <= 0)) {
						console.log(`⏭️ [收入详情页] 跳过完全空的记录 ${i + 1}`)
						continue
					}

					// 如果有产量但没有客户名称，给一个默认名称以确保能保存
					if ((!record.customer_name || record.customer_name.trim() === '') &&
						record.production > 0) {
						record.customer_name = `客户${i + 1}`
						console.log(`🔧 [收入详情页] 为有产量但无客户名的记录设置默认名称: ${record.customer_name}`)
					}

					// 其他收入和支出分配逻辑：只在第一个记录中保存全局的其他收入和支出
					const otherIncomeForRecord = (i === 0) ? (this.otherIncome || 0) : 0
					const otherCostForRecord = (i === 0) ? (this.otherCost || 0) : 0

					const salesData = {
						id: record.id && !record.id.toString().startsWith('placeholder_') ? record.id : null,
						date: this.selectedDate,
						selling_price: record.selling_price || 0,
						production: record.production || 0,
						customer_name: record.customer_name || '',
						other_income: otherIncomeForRecord,
						other_cost: otherCostForRecord,
						notes: record.notes || ''
					}

					console.log(`💾 [收入详情页] 自动保存销售记录 ${i + 1}:`, salesData)

					const result = await this.salesManager.createOrUpdateSalesRecord(salesData)

					if (result.success) {
						savedRecords.push(result.data)
						console.log(`✅ [收入详情页] 销售记录 ${i + 1} 自动保存成功`)
					} else {
						console.error(`❌ [收入详情页] 销售记录 ${i + 1} 自动保存失败:`, result.message)
						throw new Error(`自动保存顾客${i + 1}的记录失败: ${result.message}`)
					}
				}

				// 触发保存事件，通知收入页面更新
				console.log('📡 [收入详情页] 发送自动保存完成事件')

				// 添加延迟确保事件监听器已注册
				setTimeout(() => {
					uni.$emit('updateIncomeRecord', {
						date: this.selectedDate,
						workRecords: this.workRecords,
						salesRecords: savedRecords,
						totalProduction: this.totalProduction,
						timestamp: new Date().toISOString(),
						autoSaved: true, // 标记为自动保存
						type: 'salesRecordAutoSaved' // 事件类型
					})
					console.log('📡 [收入详情页] 自动保存完成事件已发送（延迟发送）')
				}, 100)

				console.log('✅ [收入详情页] 自动保存完成，已保存', savedRecords.length, '条销售记录')

			} catch (error) {
				console.error('❌ [收入详情页] 自动保存失败:', error)
				// 自动保存失败时显示提示，但不阻断流程
				uni.showToast({
					title: '自动保存失败，请手动保存',
					icon: 'error',
					duration: 2000
				})
			}
		},
	}
}
</script>

<style scoped>
.detail-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	position: relative;
	/* 适配状态栏高度 - 多种兼容方案 */
	padding-top: constant(safe-area-inset-top);
	/* iOS 11.0-11.2 */
	padding-top: env(safe-area-inset-top);
	/* iOS 11.2+ */
	/* 备用方案 */
	padding-top: var(--status-bar-height, 0px);
}

.detail-container::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
	pointer-events: none;
}

/* 顶部导航栏样式 - 参考detail-hourly.vue的稳定布局 */
.header {
	background: linear-gradient(135deg, #667eea, #764ba2);
	backdrop-filter: blur(20rpx);
	padding: 20rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);
	position: sticky;
	top: 0;
	z-index: 100;
	border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
	/* 确保导航栏不会被状态栏遮挡 - 多种兼容方案 */
	margin-top: calc(-1 * constant(safe-area-inset-top));
	/* iOS 11.0-11.2 */
	margin-top: calc(-1 * env(safe-area-inset-top));
	/* iOS 11.2+ */
	margin-top: calc(-1 * var(--status-bar-height, 0px));
	/* 备用方案 */

	padding-top: calc(20rpx + constant(safe-area-inset-top));
	/* iOS 11.0-11.2 */
	padding-top: calc(20rpx + env(safe-area-inset-top));
	/* iOS 11.2+ */
	padding-top: calc(20rpx + var(--status-bar-height, 0px));
	/* 备用方案 */
}

/* 导航栏左侧 - 参考detail-hourly.vue */
.nav-left {
	display: flex;
	align-items: center;
	gap: 10rpx;
	cursor: pointer;
}

.nav-left:active {
	opacity: 0.7;
}

.nav-icon {
	font-size: 36rpx;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.nav-text {
	font-size: 32rpx;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 导航栏标题 */
.nav-title {
	font-size: 33rpx;
	font-weight: 700;
	color: #f4ebf4;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.subtitle {
	font-size: 24rpx;
	color: #666;
	margin-top: 5rpx;
	display: block;
}

/* 导航栏右侧 */
.nav-right {
	min-width: 100rpx;
	text-align: right;
}

.action-btn {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	transition: all 0.3s ease;
	text-align: center;
}

.cancel-btn {
	background: rgb(233, 64, 64);
	border: 2rpx solid rgba(108, 117, 125, 0.3);
}

.cancel-btn:active {
	background: rgba(108, 117, 125, 0.2);
	transform: scale(0.95);
}

.save-btn {
	background: linear-gradient(135deg, #52c41a, #73d13d);
	border: 2rpx solid transparent;
}

.save-btn:active {
	transform: scale(0.95);
	opacity: 0.9;
}

.cancel-btn .btn-text {
	font-size: 28rpx;
	color: #ffffff;
	font-weight: 500;
}

.save-btn .btn-text {
	font-size: 28rpx;
	color: white;
	font-weight: 500;
}





.date-text {
	font-size: 24rpx;
	color: #999;
	margin-top: 5rpx;
	display: block;
}

.form-container {
	padding: 40rpx 30rpx 20rpx;
	max-width: 750rpx;
	margin: 0 auto;
	position: relative;
	z-index: 1;
}

.section {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10rpx);
	border-radius: 24rpx;
	padding: 35rpx 30rpx;
	margin-bottom: 25rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
}

.section:hover {
	transform: translateY(-4rpx);
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}

.section-title {
	display: flex;
	align-items: center;
	margin-bottom: 25rpx;
	position: relative;
}

.title-content {
	display: flex;
	align-items: center;
}

.section-icon {
	font-size: 36rpx;
	margin-right: 15rpx;
	filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.title-text {
	font-size: 32rpx;
	font-weight: 700;
	color: #333;
	letter-spacing: 1rpx;
}

.title-line {
	flex: 1;
	height: 3rpx;
	background: linear-gradient(90deg, #667eea, #764ba2);
	margin-left: 20rpx;
	border-radius: 2rpx;
	opacity: 0.6;
}

/* ==================== 销售模式选择器 ==================== */
.sales-mode {
	margin-bottom: 25rpx;
}

.mode-selector {
	padding: 10rpx 0;
}

.mode-options {
	display: flex;
	gap: 20rpx;
}

.mode-option {
	flex: 1;
	background: rgba(255, 255, 255, 0.8);
	border: 2rpx solid #e8eaff;
	border-radius: 16rpx;
	padding: 24rpx 20rpx;
	transition: all 0.3s ease;
	cursor: pointer;
	position: relative;
	overflow: hidden;
}

.mode-option::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #667eea, #764ba2);
	opacity: 0;
	transition: opacity 0.3s ease;
	z-index: 0;
}

.mode-option.active::before {
	opacity: 0.1;
}

.mode-option.active {
	border-color: #667eea;
	background: rgba(102, 126, 234, 0.05);
	transform: translateY(-2rpx);
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.15);
}

.mode-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8rpx;
	position: relative;
	z-index: 1;
}

.mode-label {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	transition: color 0.3s ease;
}

.mode-option.active .mode-label {
	color: #667eea;
}

.mode-indicator {
	width: 32rpx;
	height: 32rpx;
	background: #667eea;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	animation: scaleIn 0.3s ease;
}

.indicator-icon {
	color: white;
	font-size: 20rpx;
	font-weight: bold;
}

.mode-option.active .mode-desc {
	color: #555;
}

@keyframes scaleIn {
	0% {
		transform: scale(0);
		opacity: 0;
	}

	100% {
		transform: scale(1);
		opacity: 1;
	}
}

/* 表单网格布局 */
.form-grid {
	display: flex;
	flex-direction: column;
	gap: 25rpx;
}

.form-row {
	display: flex;
	gap: 20rpx;
	align-items: flex-start;
}

.form-item {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
	flex: 1;
}

.form-item.half-width {
	flex: 1;
	min-width: 0;
	/* 防止内容溢出 */
}

/* ==================== 增强表单设计 ==================== */

/* 增强表单容器 */
.enhanced-form-container {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
	padding: 10rpx 0;
}

/* 只读信息区域 */
.readonly-info-section {
	display: flex;
	gap: 20rpx;
	margin-bottom: 10rpx;
}

.info-card {
	flex: 1;
	background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
	border-radius: 16rpx;
	padding: 24rpx 20rpx;
	border: 1rpx solid #e8eaff;
	box-shadow: 0 2rpx 8rpx rgba(99, 102, 241, 0.08);
	transition: all 0.3s ease;
}

.info-card:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.12);
}

.info-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.info-icon {
	font-size: 32rpx;
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
	border-radius: 12rpx;
	box-shadow: 0 2rpx 6rpx rgba(99, 102, 241, 0.2);
}

.info-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.info-label {
	font-size: 24rpx;
	color: #6b7280;
	font-weight: 500;
}

.info-value {
	font-size: 28rpx;
	color: #1f2937;
	font-weight: 600;
}

/* 分隔线 */
.form-divider {
	height: 1rpx;
	background: linear-gradient(90deg, transparent 0%, #e5e7eb 50%, transparent 100%);
	margin: 20rpx 0;
}

/* 可编辑信息区域 */
.editable-info-section {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

/* 主要字段容器 */
.primary-field-container {
	margin-bottom: 8rpx;
}

/* 次要字段容器 */
.secondary-fields-container {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

/* 字段组通用样式 */
.field-group {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

/* 字段头部 */
.field-header {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.field-icon {
	font-size: 28rpx;
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 10rpx;
	transition: all 0.3s ease;
}

.field-icon.primary {
	background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
	box-shadow: 0 2rpx 6rpx rgba(251, 191, 36, 0.3);
}

.field-icon.secondary {
	background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
	box-shadow: 0 2rpx 6rpx rgba(156, 163, 175, 0.2);
}

.field-label {
	font-weight: 600;
	transition: color 0.3s ease;
}

.field-label.primary {
	font-size: 30rpx;
	color: #92400e;
}

.field-label.secondary {
	font-size: 28rpx;
	color: #374151;
}

.field-label.required::after {
	content: '*';
	color: #ef4444;
	margin-left: 6rpx;
	font-weight: bold;
	font-size: 32rpx;
}

.field-unit {
	font-size: 24rpx;
	color: #6b7280;
	font-weight: 500;
	margin-left: auto;
}

/* 增强输入框包装器 */
.enhanced-input-wrapper {
	position: relative;
	border-radius: 14rpx;
	transition: all 0.3s ease;
	overflow: hidden;
}

.enhanced-input-wrapper.primary {
	background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
	border: 2rpx solid #f59e0b;
	box-shadow: 0 4rpx 12rpx rgba(245, 158, 11, 0.15);
}

.enhanced-input-wrapper.secondary {
	background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
	border: 2rpx solid #d1d5db;
	box-shadow: 0 2rpx 8rpx rgba(156, 163, 175, 0.1);
}

.enhanced-input-wrapper:focus-within {
	transform: translateY(-2rpx);
}

.enhanced-input-wrapper.primary:focus-within {
	border-color: #d97706;
	box-shadow: 0 6rpx 16rpx rgba(245, 158, 11, 0.25);
}

.enhanced-input-wrapper.secondary:focus-within {
	border-color: #9ca3af;
	box-shadow: 0 4rpx 12rpx rgba(156, 163, 175, 0.15);
}

/* 增强输入框 */
.enhanced-input {
	width: 100%;
	height: 88rpx;
	padding: 0 24rpx;
	border: none;
	background: transparent;
	font-size: 28rpx;
	font-weight: 500;
	transition: all 0.3s ease;
	outline: none;
}

.enhanced-input.primary {
	color: #92400e;
	font-weight: 600;
}

.enhanced-input.secondary {
	color: #374151;
}

.enhanced-input::placeholder {
	color: #9ca3af;
	font-weight: 400;
}

.enhanced-input.primary::placeholder {
	color: #d97706;
	opacity: 0.7;
}

/* 主要字段特殊样式 */
.primary-field {
	background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
	border-radius: 16rpx;
	padding: 24rpx;
	border: 1rpx solid #fbbf24;
	box-shadow: 0 4rpx 12rpx rgba(251, 191, 36, 0.1);
	position: relative;
	overflow: hidden;
}

.primary-field::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 3rpx;
	background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%);
}

/* 次要字段样式 */
.secondary-field {
	background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
	border-radius: 14rpx;
	padding: 20rpx;
	border: 1rpx solid #e5e7eb;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
	transition: all 0.3s ease;
}

.secondary-field:hover {
	transform: translateY(-1rpx);
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 可编辑字段样式 */
.form-item .label.required::after {
	content: '*';
	color: #ff4757;
	margin-left: 5rpx;
	font-weight: bold;
}

.editable-wrapper {
	position: relative;
}

.editable-input {
	width: 100%;
	height: 80rpx;
	padding: 0 20rpx;
	border: 2rpx solid #e1e8ed;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: white;
	transition: all 0.3s ease;
}

/* 紧凑型输入框 - 优化宽度 */
.compact-input {
	max-width: 280rpx;
	min-width: 200rpx;
}

.editable-input:focus {
	border-color: #667eea;
	box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
	outline: none;
}

/* 只读字段样式 */
.readonly-wrapper {
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border: 2rpx solid #dee2e6;
	border-radius: 12rpx;
	padding: 20rpx;
}

.readonly-text {
	font-size: 28rpx;
	color: #495057;
	font-weight: 500;
}

/* 收益汇总卡片样式 */
.summary-cards {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.summary-card {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx;
	border-radius: 16rpx;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.summary-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	opacity: 0.1;
	transition: opacity 0.3s ease;
}

.income-card {
	background: linear-gradient(135deg, #52c41a, #73d13d);
}

.cost-card {
	background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.tea-picking-cost-card {
	background: linear-gradient(135deg, #52c41a, #73d13d);
	color: white;
}

.labor-cost-card {
	background: linear-gradient(135deg, #1890ff, #40a9ff);
	color: white;
}

.profit-card.positive-profit {
	background: linear-gradient(135deg, #f56a00, #fa8c16);
	color: white;
}

.profit-card.negative-profit {
	background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.card-header {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.card-icon {
	font-size: 32rpx;
}

.card-title {
	font-size: 28rpx;
	font-weight: 600;
}

.card-value {
	text-align: right;
}

.value-amount {
	font-size: 32rpx;
	font-weight: 700;
	display: block;
}

.tea-picking-cost-amount {
	color: white;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.labor-cost-amount {
	color: white;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.profit-rate {
	font-size: 24rpx;
	opacity: 0.9;
	margin-top: 5rpx;
	display: block;
}

.form-item.readonly {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 15rpx;
}

/* 支出明细样式 */
.cost-grid {
	display: flex;
	flex-direction: column;
	gap: 25rpx;
}

.cost-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx;
	border-radius: 16rpx;
	transition: all 0.3s ease;
}

.cost-item.readonly {
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border: 2rpx solid #dee2e6;
}

.cost-item.editable {
	background: linear-gradient(135deg, #fff5f5, #ffe8e8);
	border: 2rpx solid #fbb6ce;
}

.cost-label {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.label-icon {
	font-size: 28rpx;
}

.cost-value {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.readonly-value .value-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #495057;
}

.editable-value {
	position: relative;
}

.value-input {
	width: 200rpx;
	height: 60rpx;
	padding: 0 15rpx;
	border: 2rpx solid #e1e8ed;
	border-radius: 8rpx;
	font-size: 26rpx;
	text-align: right;
	background: white;
}

.value-input:focus {
	border-color: #667eea;
	outline: none;
}

.currency {
	font-size: 24rpx;
	color: #6c757d;
	font-weight: 500;
}

.label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.input-wrapper {
	flex: 1;
	position: relative;
}

.input {
	width: 100%;
	height: 70rpx;
	padding: 0 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: white;
}

.input:focus {
	border-color: #2e7d32;
	outline: none;
}

.input-text {
	display: block;
	height: 70rpx;
	line-height: 70rpx;
	padding: 0 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: white;
	color: #333;
}

.input-text.calculated {
	background: #f8f9fa;
	border-color: #f0f0f0;
	font-weight: 600;
}

.positive {
	color: #2e7d32;
}

.negative {
	color: #d32f2f;
}



/* 响应式设计 */
@media (max-width: 750rpx) {
	.header {
		padding: 20rpx;
		/* 移动端状态栏安全区域适配 - 多种兼容方案 */
		padding-top: calc(20rpx + constant(safe-area-inset-top));
		/* iOS 11.0-11.2 */
		padding-top: calc(20rpx + env(safe-area-inset-top));
		/* iOS 11.2+ */
		padding-top: calc(20rpx + var(--status-bar-height, 0px));
		/* 备用方案 */
	}

	/* 小屏幕导航栏适配 - 参考detail-hourly.vue */
	.nav-icon {
		font-size: 32rpx;
	}

	.nav-text {
		font-size: 28rpx;
	}

	.nav-title {
		font-size: 36rpx;
	}

	.action-btn {
		padding: 10rpx 20rpx;
		font-size: 26rpx;
	}

	.btn-text {
		font-size: 24rpx !important;
	}

	.form-container {
		padding: 30rpx 20rpx 20rpx;
	}

	.section {
		padding: 30rpx 25rpx;
		margin-bottom: 20rpx;
	}

	.form-grid {
		gap: 20rpx;
	}

	.form-row {
		flex-direction: column;
		gap: 15rpx;
	}

	.form-item.half-width {
		max-width: none;
	}

	.compact-input {
		max-width: none;
		min-width: auto;
	}

	.cost-item {
		flex-direction: column;
		align-items: stretch;
		gap: 12rpx;
	}

	.cost-value {
		justify-content: flex-end;
	}

	.value-input {
		width: 150rpx;
	}

	.summary-cards {
		gap: 12rpx;
	}

	.summary-card {
		padding: 20rpx;
	}

	.card-header {
		gap: 8rpx;
	}

	.card-icon {
		font-size: 26rpx;
	}

	.card-title {
		font-size: 24rpx;
	}

	.value-amount {
		font-size: 26rpx;
	}

	.profit-rate {
		font-size: 20rpx;
	}

	/* ==================== 增强表单响应式样式 ==================== */

	/* 只读信息区域响应式 */
	.readonly-info-section {
		flex-direction: column;
		gap: 16rpx;
	}

	.info-card {
		padding: 20rpx 16rpx;
		border-radius: 12rpx;
	}

	.info-icon {
		font-size: 28rpx;
		width: 40rpx;
		height: 40rpx;
		border-radius: 10rpx;
	}

	.info-label {
		font-size: 22rpx;
	}

	.info-value {
		font-size: 26rpx;
	}

	/* 可编辑区域响应式 */
	.editable-info-section {
		gap: 20rpx;
	}

	.primary-field {
		padding: 20rpx;
		border-radius: 14rpx;
	}

	.secondary-field {
		padding: 16rpx;
		border-radius: 12rpx;
	}

	.field-icon {
		font-size: 24rpx;
		width: 36rpx;
		height: 36rpx;
		border-radius: 8rpx;
	}

	.field-label.primary {
		font-size: 28rpx;
	}

	.field-label.secondary {
		font-size: 26rpx;
	}

	.field-unit {
		font-size: 22rpx;
	}

	.enhanced-input {
		height: 80rpx;
		padding: 0 20rpx;
		font-size: 26rpx;
	}

	.enhanced-input-wrapper {
		border-radius: 12rpx;
	}
}

/* 大屏幕优化 */
@media (min-width: 750rpx) {
	.readonly-info-section {
		gap: 24rpx;
	}

	.info-card {
		padding: 28rpx 24rpx;
	}

	.secondary-fields-container {
		display: flex;
		flex-direction: row;
		gap: 24rpx;
	}

	.secondary-field {
		flex: 1;
	}

	.enhanced-input-wrapper:hover {
		transform: translateY(-1rpx);
	}

	.enhanced-input-wrapper.primary:hover {
		box-shadow: 0 8rpx 20rpx rgba(245, 158, 11, 0.2);
	}

	.enhanced-input-wrapper.secondary:hover {
		box-shadow: 0 6rpx 16rpx rgba(156, 163, 175, 0.12);
	}
}

/* ==================== 工作记录信息样式 ==================== */
.work-record-info {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

/* ==================== 销售记录列表样式 ==================== */
.sales-records {
	margin-top: 32rpx;
}

.sales-count {
	font-size: 22rpx;
	color: #666;
	margin-left: 8rpx;
}

.production-summary {
	background: #f8f9fa;
	padding: 20rpx;
	border-radius: 12rpx;
	margin-bottom: 24rpx;
}

.time-period-breakdown {
	margin-bottom: 16rpx;
	padding-bottom: 16rpx;
	border-bottom: 1rpx solid #e0e0e0;
}

.breakdown-title {
	margin-bottom: 12rpx;
}

.breakdown-label {
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
}

.breakdown-items {
	display: flex;
	gap: 20rpx;
	flex-wrap: wrap;
}

.breakdown-item {
	display: flex;
	align-items: center;
	gap: 6rpx;
	padding: 6rpx 12rpx;
	background: white;
	border-radius: 8rpx;
	border: 1rpx solid #e0e0e0;
}

.period-label {
	font-size: 22rpx;
	color: #666;
}

.period-value {
	font-size: 22rpx;
	font-weight: 600;
	color: #3498db;
}

.allocation-status {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.summary-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.summary-label {
	font-size: 24rpx;
	color: #666;
}

.summary-value {
	font-size: 26rpx;
	font-weight: 600;
	color: #2c3e50;
}

.summary-value.warning {
	color: #e74c3c;
}

.summary-value.error {
	color: #e74c3c;
	font-weight: 700;
}

.summary-value.success {
	color: #27ae60;
}

.production-summary.error {
	border: 2rpx solid #e74c3c;
	background: #ffeaea;
}

.validation-message {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-top: 12rpx;
	padding: 8rpx 12rpx;
	background: #fee;
	border-radius: 8rpx;
	border-left: 4rpx solid #e74c3c;
}

.error-icon {
	font-size: 20rpx;
}

.error-text {
	font-size: 22rpx;
	color: #e74c3c;
	font-weight: 500;
}

.field-input.error {
	border-color: #e74c3c;
	background: #ffeaea;
}

.error-tip {
	position: absolute;
	top: 100%;
	left: 0;
	font-size: 20rpx;
	color: #e74c3c;
	margin-top: 4rpx;
	padding: 4rpx 8rpx;
	background: #fee;
	border-radius: 4rpx;
	white-space: nowrap;
}

.sales-record-list {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

/* 日期工作汇总样式 */
.daily-summary {
	margin-bottom: 32rpx;
}

.daily-summary-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20rpx;
	padding: 32rpx;
	color: white;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.summary-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.date-display {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.date-icon {
	font-size: 32rpx;
}

.date-text {
	font-size: 32rpx;
	font-weight: 600;
}

.worker-count {
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: rgba(255, 255, 255, 0.2);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.count-icon {
	font-size: 24rpx;
}

.count-text {
	font-size: 24rpx;
	font-weight: 500;
}

.workers-list {
	margin-bottom: 24rpx;
}

.workers-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.expand-btn {
	background: rgba(255, 255, 255, 0.3);
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	/* 确保在移动端可点击 */
	min-width: 80rpx;
	min-height: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	/* 防止文本选择 */
	user-select: none;
	-webkit-user-select: none;
	/* 触摸反馈 */
	-webkit-tap-highlight-color: rgba(255, 255, 255, 0.2);
}

.expand-btn:active {
	background: rgba(255, 255, 255, 0.5);
	transform: scale(0.95);
}

.expand-text {
	font-size: 20rpx;
	color: rgba(255, 255, 255, 0.9);
	font-weight: 500;
}

.workers-title {
	font-size: 24rpx;
	opacity: 0.9;
	font-weight: 500;
}

.workers-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
}

.worker-tag {
	background: rgba(255, 255, 255, 0.2);
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.worker-name {
	font-size: 24rpx;
	font-weight: 500;
}

.worker-production {
	font-size: 22rpx;
	opacity: 0.8;
}

.summary-stats {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.stats-row {
	display: flex;
	justify-content: space-between;
	gap: 16rpx;
}

.stat-item {
	flex: 1;
	background: rgba(255, 255, 255, 0.15);
	padding: 20rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.stat-icon {
	font-size: 28rpx;
}

.stat-content {
	flex: 1;
}

.stat-label {
	font-size: 22rpx;
	opacity: 0.8;
	display: block;
	margin-bottom: 4rpx;
}

.stat-value {
	font-size: 26rpx;
	font-weight: 600;
	display: block;
}

.stat-value.primary {
	color: #fff;
	font-size: 28rpx;
}

.stat-value.secondary {
	color: #ffd700;
}

/* 销售记录管理样式优化 */
.sales-summary {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-left: 12rpx;
}

.sales-count {
	background: #e3f2fd;
	color: #1976d2;
	padding: 6rpx 14rpx;
	border-radius: 12rpx;
	font-size: 26rpx;
	font-weight: 500;
}

.sales-allocation {
	background: #f3e5f5;
	color: #7b1fa2;
	padding: 6rpx 14rpx;
	border-radius: 12rpx;
	font-size: 26rpx;
	font-weight: 500;
}

/* 紧凑型销售记录列表 */
.compact-sales-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.compact-sales-item {
	background: white;
	border-radius: 12rpx;
	padding: 16rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.compact-sales-item.error {
	border-color: #ff5722;
	background: #fff3f3;
}

.item-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.customer-badge {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: 600;
}

.customer-number {
	color: white;
	font-size: 26rpx;
}

.item-actions {
	display: flex;
	align-items: center;
}

.delete-btn {
	background: #ffebee;
	color: #f44336;
	width: 44rpx;
	height: 44rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: bold;
	transition: all 0.3s ease;
}

.delete-btn:active {
	background: #ffcdd2;
	transform: scale(0.9);
}

.delete-icon {
	color: #f44336;
}

/* 紧凑型输入区域 */
.compact-inputs {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.input-group {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.input-label {
	font-size: 28rpx;
	color: #666;
	min-width: 70rpx;
	font-weight: 500;
}

.input-container {
	flex: 1;
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 8rpx;
	padding: 0 12rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.input-container:focus-within {
	background: white;
	border-color: #667eea;
	box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

.compact-input {
	flex: 1;
	border: none;
	background: transparent;
	padding: 12rpx 0;
	font-size: 30rpx;
	color: #333;
}

.compact-input.error {
	color: #f44336;
}

.compact-input::placeholder {
	color: #bbb;
	font-size: 28rpx;
}

.input-unit {
	font-size: 26rpx;
	color: #999;
	margin-left: 8rpx;
	min-width: 45rpx;
}

.name-input {
	font-size: 28rpx;
}

/* 输入组特定样式 */
.production-group .input-container {
	background: #e8f5e8;
}

.production-group .input-container:focus-within {
	border-color: #4caf50;
	box-shadow: 0 0 0 4rpx rgba(76, 175, 80, 0.1);
}

.price-group .input-container {
	background: #fff3e0;
}

.price-group .input-container:focus-within {
	border-color: #ff9800;
	box-shadow: 0 0 0 4rpx rgba(255, 152, 0, 0.1);
}

.name-group .input-container {
	background: #f3e5f5;
}

.name-group .input-container:focus-within {
	border-color: #9c27b0;
	box-shadow: 0 0 0 4rpx rgba(156, 39, 176, 0.1);
}

/* 项目底部 */
.item-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 12rpx;
	padding-top: 12rpx;
	border-top: 1rpx solid #f0f0f0;
}

.subtotal-info {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.subtotal-amount {
	font-size: 32rpx;
	font-weight: 600;
	color: #4caf50;
}

.error-text {
	font-size: 24rpx;
	color: #f44336;
	background: #ffebee;
	padding: 4rpx 10rpx;
	border-radius: 8rpx;
}

/* 添加按钮区域 */
.add-section {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
	margin-top: 16rpx;
}

.add-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	padding: 16rpx 24rpx;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	font-size: 26rpx;
	font-weight: 500;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.add-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

/* 移除禁用状态，始终允许添加客户 */

.add-icon {
	font-size: 32rpx;
	font-weight: bold;
}

.add-text {
	font-size: 30rpx;
}

.allocation-status {
	text-align: center;
}

.status-text {
	font-size: 28rpx;
	color: #666;
	padding: 10rpx 18rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
	display: inline-block;
}

.status-text.warning {
	color: #f44336;
	background: #ffebee;
}

.action-btn {
	padding: 8rpx 12rpx;
	border-radius: 8rpx;
	cursor: pointer;
}

.delete-btn {
	background: #fee;
	color: #e74c3c;
}

.btn-icon {
	font-size: 20rpx;
}

.card-content {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.field-group {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.field-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.field-label {
	font-size: 24rpx;
	color: #555;
	font-weight: 500;
}

.field-actions {
	display: flex;
	gap: 12rpx;
}

.auto-fill-btn {
	font-size: 22rpx;
	color: #3498db;
	padding: 4rpx 8rpx;
	border-radius: 4rpx;
	background: #e3f2fd;
	cursor: pointer;
}

.input-wrapper {
	position: relative;
}

.field-input {
	width: 100%;
	padding: 16rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 26rpx;
	background: #fafafa;
	transition: all 0.3s ease;
}

.field-input:focus {
	border-color: #3498db;
	background: white;
	box-shadow: 0 0 0 4rpx rgba(52, 152, 219, 0.1);
}

.subtotal {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12rpx 16rpx;
	background: #f0f8ff;
	border-radius: 8rpx;
	margin-top: 8rpx;
}

.subtotal-label {
	font-size: 24rpx;
	color: #666;
}

.subtotal-value {
	font-size: 26rpx;
	font-weight: 600;
	color: #3498db;
}

.add-sales-section {
	margin-top: 24rpx;
	text-align: center;
}

.add-sales-btn {
	display: inline-flex;
	align-items: center;
	gap: 8rpx;
	padding: 16rpx 32rpx;
	background: linear-gradient(135deg, #3498db, #2980b9);
	color: white;
	border-radius: 12rpx;
	font-size: 26rpx;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}

.add-sales-btn:active {
	transform: scale(0.98);
}

.add-sales-btn.disabled {
	background: #bdc3c7;
	cursor: not-allowed;
}

.add-icon {
	font-size: 24rpx;
}

.add-text {
	font-size: 24rpx;
}

.add-tip {
	display: block;
	margin-top: 12rpx;
	font-size: 22rpx;
	color: #999;
}
</style>
